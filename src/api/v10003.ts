import {
  Activity10003CreateOrUpdateRequest,
  Activity10003CreateOrUpdateResponse,
  Activity10003DrawChanceRecordRequest,
  Activity10003GetDrawCodeRequest,
  Activity10003InviteDetailRequest,
  Activity10003InviteDetailResponse,
  Activity10003PhysicalPrizeInfoRequest,
  Activity10003PrizeInfoRequest,
  Activity10003PrizeInfoResponse,
  Activity10003PrizeLogRequest,
  Activity10003PrizeLogResponse,
  ActivityAddLotteryNumRequest,
  ActivityDataBasicRequest,
  ActivityDataBasicTotalResponse,
  ActivityDataByDayPage,
  IPageActivity10003DrawChanceRecordResponse,
  IPageActivity10003GetDrawCodeResponse,
  IPageActivity10003PhysicalPrizeInfoResponse,
} from './types';

import { request as httpRequest } from 'ice';

/**
 * @tags 抽签码活动抽奖
 * @summary 添加奖品分数
 * @request POST:/10003/addLotteryNum
 */
export const addLotteryNum = (request: ActivityAddLotteryNumRequest): Promise<boolean> => {
  return httpRequest({
    url: '/10003/addLotteryNum',
    method: 'post',
    data: request,
  });
};

/**
 * @tags 抽签码活动抽奖
 * @summary 创建抽签码活动抽奖活动
 * @request POST:/10003/createActivity
 */
export const createActivity = (
  activity10003CreateOrUpdateRequest: Activity10003CreateOrUpdateRequest,
): Promise<Activity10003CreateOrUpdateResponse> => {
  return httpRequest({
    url: '/10003/createActivity',
    method: 'post',
    data: activity10003CreateOrUpdateRequest,
  });
};

/**
 * @tags 抽签码活动抽奖数据
 * @summary 获取ByDay活动统计数据
 * @request POST:/10003/data/getByDayData
 */
export const dataGetByDayData = (request: ActivityDataBasicRequest): Promise<ActivityDataByDayPage> => {
  return httpRequest({
    url: '/10003/data/getByDayData',
    method: 'post',
    data: request,
  });
};

/**
 * @tags 抽签码活动抽奖数据
 * @summary 导出ByDay活动统计数据
 * @request POST:/10003/data/getByDayData/export
 */
export const dataGetByDayDataExport = (request: ActivityDataBasicRequest): Promise<void> => {
  return httpRequest({
    url: '/10003/data/getByDayData/export',
    method: 'post',
    data: request,
  });
};

/**
 * @tags 抽签码活动抽奖数据
 * @summary 导出获取抽签码记录
 * @request POST:/10003/data/getDrawChanceRecord/export
 */
export const dataGetDrawChanceRecordExport = (request: Activity10003GetDrawCodeRequest): Promise<void> => {
  return httpRequest({
    url: '/10003/data/getDrawChanceRecord/export',
    method: 'post',
    data: request,
  });
};

/**
 * @tags 抽签码活动抽奖数据
 * @summary 获取抽签码记录
 * @request POST:/10003/data/getDrawCode
 */
export const dataGetDrawCode = (
  request: Activity10003GetDrawCodeRequest,
): Promise<IPageActivity10003GetDrawCodeResponse> => {
  return httpRequest({
    url: '/10003/data/getDrawCode',
    method: 'post',
    data: request,
  });
};

/**
 * @tags 抽签码活动抽奖数据
 * @summary 奖品修改记录
 * @request POST:/10003/data/getInviteDetail
 */
export const dataGetInviteDetail = (
  request: Activity10003InviteDetailRequest,
): Promise<Activity10003InviteDetailResponse[]> => {
  return httpRequest({
    url: '/10003/data/getInviteDetail',
    method: 'post',
    data: request,
  });
};

/**
 * @tags 抽签码活动抽奖数据
 * @summary 获取实物中奖信息
 * @request POST:/10003/data/getPhysicalPrizeInfo
 */
export const dataGetPhysicalPrizeInfo = (
  activity10003PhysicalPrizeInfoRequest: Activity10003PhysicalPrizeInfoRequest,
): Promise<IPageActivity10003PhysicalPrizeInfoResponse> => {
  return httpRequest({
    url: '/10003/data/getPhysicalPrizeInfo',
    method: 'post',
    data: activity10003PhysicalPrizeInfoRequest,
  });
};

/**
 * @tags 抽签码活动抽奖数据
 * @summary 导出实物中奖信息
 * @request POST:/10003/data/getPhysicalPrizeInfo/export
 */
export const dataGetPhysicalPrizeInfoExport = (request: Activity10003PhysicalPrizeInfoRequest): Promise<void> => {
  return httpRequest({
    url: '/10003/data/getPhysicalPrizeInfo/export',
    method: 'post',
    data: request,
  });
};

/**
 * @tags 抽签码活动抽奖数据
 * @summary 获取奖品数据
 * @request POST:/10003/data/getPrizeInfo
 */
export const dataGetPrizeInfo = (request: Activity10003PrizeInfoRequest): Promise<Activity10003PrizeInfoResponse[]> => {
  return httpRequest({
    url: '/10003/data/getPrizeInfo',
    method: 'post',
    data: request,
  });
};

/**
 * @tags 抽签码活动抽奖数据
 * @summary 奖品修改记录
 * @request POST:/10003/data/getPrizeLog
 */
export const dataGetPrizeLog = (request: Activity10003PrizeLogRequest): Promise<Activity10003PrizeLogResponse[]> => {
  return httpRequest({
    url: '/10003/data/getPrizeLog',
    method: 'post',
    data: request,
  });
};

/**
 * @tags 抽签码活动抽奖数据
 * @summary 获取活动统计数据
 * @request POST:/10003/data/getTotalData
 */
export const dataGetTotalData = (request: ActivityDataBasicRequest): Promise<ActivityDataBasicTotalResponse[]> => {
  return httpRequest({
    url: '/10003/data/getTotalData',
    method: 'post',
    data: request,
  });
};

/**
 * @tags 抽签码活动抽奖数据
 * @summary 获取中奖记录
 * @request POST:/10003/data/getWinningRecord
 */
export const dataGetWinningRecord = (
  request: Activity10003DrawChanceRecordRequest,
): Promise<IPageActivity10003DrawChanceRecordResponse> => {
  return httpRequest({
    url: '/10003/data/getWinningRecord',
    method: 'post',
    data: request,
  });
};

/**
 * @tags 抽签码活动抽奖数据
 * @summary 导出中奖记录
 * @request POST:/10003/data/getWinningRecord/export
 */
export const dataGetWinningRecordExport = (request: Activity10003DrawChanceRecordRequest): Promise<void> => {
  return httpRequest({
    url: '/10003/data/getWinningRecord/export',
    method: 'post',
    data: request,
  });
};

/**
 * @tags 抽签码活动抽奖
 * @summary 修改抽签码活动抽奖活动
 * @request POST:/10003/updateActivity
 */
export const updateActivity = (
  activity10003CreateOrUpdateRequest: Activity10003CreateOrUpdateRequest,
): Promise<Activity10003CreateOrUpdateResponse> => {
  return httpRequest({
    url: '/10003/updateActivity',
    method: 'post',
    data: activity10003CreateOrUpdateRequest,
  });
};
