import { YiliActivityReponse, YiliActivityRequest } from './types';

import { request as httpRequest } from 'ice';

/**
 * @tags 伊利活动控制器
 * @summary 修改投放活动
 * @request POST:/yili/activity/createActivityEffective
 */
export const activityCreateActivityEffective = (request: YiliActivityRequest): Promise<void> => {
  return httpRequest({
    url: '/yili/activity/createActivityEffective',
    method: 'post',
    data: request,
  });
};

/**
 * @tags 伊利活动控制器
 * @summary 获取生效中的活动
 * @request POST:/yili/activity/getActivityEffective
 */
export const activityGetActivityEffective = (request: YiliActivityRequest): Promise<YiliActivityReponse[]> => {
  return httpRequest({
    url: '/yili/activity/getActivityEffective',
    method: 'post',
    data: request,
  });
};
