import Container from '@/components/Container';
import { Form, Button, Box, Input, Field } from '@alifd/next';
import { useActivity } from '../../reducer';

export default function Rule({ goNextStep, goPrevStep }) {
  const { state, dispatch } = useActivity();
  const field = Field.useField();
  const { operationType } = state.extra;
  const needDisable = operationType === 'view';

  const updateRule = (data: string) => {
    dispatch({ type: 'UPDATE_RULE', payload: data });
  };

  const handleSubmit = () => {
    field.validatePromise().then(({ errors }) => {
      if (errors) return;
      goNextStep();
    });
  };

  const handleBack = () => {
    goPrevStep();
  };

  return (
    <Form className="activity-rule-form" field={field}>
      <Container title={'活动规则'}>
        <Box direction="row" justify="flex-end" align="center" spacing={10} margin={[0, 0, 10, 0]}>
          <span style={{ color: 'var(--color-text-secondary)' }}>
            提交活动前请核对活动规则
          </span>
        </Box>

        <Form.Item name="rule" label="" required requiredMessage={'请输入活动规则'}>
          <Input.TextArea
            maxLength={2000}
            disabled={needDisable}
            rows={24}
            cutString
            showLimitHint
            composition
            value={state.rule}
            onChange={(val) => updateRule(val)}
            placeholder="请输入活动规则"
          />
        </Form.Item>
      </Container>

      <Box direction="row" justify="center" spacing={16} margin={[-30, 0, 0, 0]}>
        <Button onClick={handleBack}>上一步</Button>
        <Button type="primary" style={{ width: 150 }} onClick={handleSubmit}>
          下一步：氛围定制
        </Button>
      </Box>
    </Form>
  );
}


