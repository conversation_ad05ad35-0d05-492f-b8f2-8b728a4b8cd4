/* 为当前页面的 phone-preview-content 添加隐藏滚动条样式 */
:global(.phone-preview-content) {
  // 隐藏滚动条但保留滚动功能
  scrollbar-width: none; // Firefox
  -ms-overflow-style: none; // IE/Edge

  &::-webkit-scrollbar {
    display: none; // Chrome/Safari/Opera
  }
}

.main {
  position: relative;
  background-color: var(--actBgColor);

  .kv {
    width: 100%;
  }

  .btns {
    position: absolute;
    right: -1px;
    top: 140px;

    div {
      background-image: var(--ruleBtn);
      background-repeat: no-repeat;
      background-size: 100%;
      color: var(--ruleBtnColor);
      border-top-left-radius: 10px;
      border-bottom-left-radius: 10px;
      margin-bottom: 8px;
      padding: 3px 0 0 8px;
      font-size: 10px;
      width: 50px;
      height: 22px;
    }
  }

  .userInfoBg {
    width: 98%;
    height: 90px;
    background-image: var(--userInfoBg);
    background-repeat: no-repeat;
    background-size: 100%;
    padding: 20px;

    .userInfo{
      font-size: 11px;
      color: var(--userInfoColor);
      transform: rotate(-3deg);
    }
  }

  .step1Bg {
    background-image: var(--step1Bg);
    background-repeat: no-repeat;
    background-size: 100%;
    width: 280px;
    height: 210px;
    margin: 10px auto 0;
    padding: 70px 6px 0 6px;
    display: flex;
    flex-direction: column;
    align-items: center;
    color: #cc0607;

    .tabContainer {
      width: 50px;
      height: 80px;
      margin-top: 5px;
    }
  }

  .step2Bg {
    width: 280px;
    height: auto;
    padding: 55px 0 40px 0;
    margin: 10px auto 0;
    position: relative;
    top: 0;
    background-image: var(--step2Bg);
    background-size: 100%;
    background-repeat: no-repeat;
  }
  .step2ItemBox{
    position: relative;
    top: 0;
    left: 50%;
    transform: translateX(-52%);
    background-image: var(--step2ItemBg);
    width: 250px;
    height: 150px;
    background-repeat: no-repeat;
    background-size: 100%;
    overflow: hidden;
    padding: 0 10px;
    overflow-x: scroll;
    scroll-behavior: smooth;
    // 隐藏滚动条但保留滚动功能
    scrollbar-width: none;
    -ms-overflow-style: none;
    &::-webkit-scrollbar {
      width: 0;
      height: 0;
    }
  }

  .actNum{
    position: absolute;
    top: 17px;
    font-size: 10px;
    color: #ffffff;
    left: 7px;
    width: 22px;
    height: 12px;
    //background: aqua;
    text-align: center;
  }
  .actTitle{
    position: absolute;
    top: 10px;
    font-size: 10px;
    color: #cc0607;
    left: 33px;
    width: 63px;
    height: 19px;
    //background: aqua;
    text-align: left;
    line-height: 18px;
  }
  .itemBox{
    width: 230px;
    height: 100%;
    display: flex;
    gap: 10px;
    padding: 34px 0 0 12px;
    overflow: hidden;
    overflow-x: scroll;
    // 隐藏滚动条但保留滚动功能
    scrollbar-width: none;
    -ms-overflow-style: none;
    &::-webkit-scrollbar {
      width: 0;
      height: 0;
    }
  }
  .step2Item{
    width: 76px;
    height: 110px;
    background-image: url("https://img10.360buyimg.com/imgzone/jfs/t1/302602/17/19449/3999/68a6c427Ff64d3146/c6acbfeca767f6b1.png");
    background-repeat: no-repeat;
    background-size: 100%;
    margin: 0 auto;
    padding-top: 10px;
    .prizeImg {
      height: 70px;
      margin: 0 auto 5.4px;
      display: block;
      width: auto;
    }
    .getPrizeBtn{
      width: 76px;
      height: 15px;
      margin: 0 auto;
      background-size: 100%;
      background-repeat: no-repeat;
      line-height: 15px;
      background-image: var(--getDemoPrizeBtn);
      border-radius: 2px;
      //background-color: #3d7fff;
    }
    .price{
      width: 76px;
      text-align: center;
      color:#fff;
      font-size: 9px;
    }
  }

  .getDemoPrizeBtn {
    background-image: var(--getDemoPrizeBtn);
    background-repeat: no-repeat;
    background-size: 100% 100%;
    width: 80px;
    height: 26px;
    cursor: pointer;
    transition: all 0.3s ease;
    flex-shrink: 0; // 防止tab被压缩

    .stepText {
      color: #fdf5bd;
      margin: 0 auto;
      text-align: center;
      line-height: 22px;
      font-size: 12px;
    }
  }

  .step3Bg {
    background-image: var(--step3Bg);
    background-repeat: no-repeat;
    background-size: 100%;
    width: 280px;
    height: 610px;
    margin: 0 auto;
    padding: 58px 0 0;
  }

  .repurchasePrizeBox {
    width: 247px;
    height: 164px;
    margin: 0 0 0 19.8px;
    padding: 20px 10px 0;
    display: flex;
    gap: 10px;
    overflow: hidden;
    overflow-x: scroll;
    scrollbar-width: none;
    -ms-overflow-style: none;
    &::-webkit-scrollbar {
      width: 0;
      height: 0;
    }
  }

  .repurchasePrizeItem{
    width: 124px;
    height: 164px;
    margin: 0 auto;

  }

  .lotteryImg{
    width: 90px;
    height: 90px;
    display: block;
    margin: 0 auto;
  }

  .getRepurchasePrizeBtn{
    background-image: url("https://img10.360buyimg.com/imgzone/jfs/t1/331951/12/1735/10177/68a6825fFeb9fad91/fa101fd94d922d3f.png");
    background-repeat: no-repeat;
    background-size: 100%;
    width: 80px;
    height: 22px;
    margin: 10px auto 0;
  }

  .repurchaseSkuBox {
    width: 247px;
    height: 320px;
    margin: 50px 0 0 19px;
    display: grid;
    grid-template-columns: repeat(2, 110px);
    justify-content: center;
    gap: 14px;
    overflow: hidden;
    overflow-y: scroll;
    scrollbar-width: none;
    -ms-overflow-style: none;
    &::-webkit-scrollbar {
      width: 0;
      height: 0;
    }
    .repurchaseSkuItem{
      width: 110px;
      height: 140px;
      margin: 0 auto;
      padding: 10px 0 0;
      background-color: #fff;
      border-radius: 12px;
      overflow: hidden;
      position: relative;
      .skuPicture{
        width: auto;
        height: 80px;
        margin: 0 auto;
        display: block;
      }
      .skuName {
        margin: 2px auto 0;
        height: 24px;
        font-size: 10px;
        text-align: center;
        width: 80px;
        overflow: hidden;
        text-overflow: ellipsis;
        display: -webkit-box;
        -webkit-line-clamp: 2;
        -webkit-box-orient: vertical;
      }
      .skuBtn {
        position: absolute;
        background-image: url("https://img10.360buyimg.com/imgzone/jfs/t1/328312/31/8718/9727/68a6d8b7F6b160a3e/b6a295ed7c869b2d.png");
        width: 80px;
        height: 22px;
        background-repeat: no-repeat;
        background-size: 100%;
        bottom: 2px;
        left: 50%;
        transform: translateX(-50%);
      }
    }
  }

  .ruleImage{
    background-image: var(--ruleImage);
    background-repeat: no-repeat;
    background-size: 100%;
    width: 280px;
    height: 600px;
    margin: 0 auto;
  }

  .bottomToTop {
    background-image: var(--bottomToTop);
    background-repeat: no-repeat;
    background-size: 100%;
    width: 140px;
    height: 46px;
    margin: 0 auto;
  }
}