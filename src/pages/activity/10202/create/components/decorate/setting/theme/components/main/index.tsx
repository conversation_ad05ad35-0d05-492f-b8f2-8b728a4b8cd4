import ImgUpload from '@/components/ImgUpload';
import { Box } from '@alifd/next';
import { ThemeState } from '../../../../type';
import ColorPickerFormItem from '@/components/ColorPickerFormItem/ColorPickerFormItem';

export default function Main(props) {
  const { tState, tDispatch, state } = props;
  const {
      kv,
      actBgColor,
      ruleBtn,
      ruleBtnColor,
      userInfoBg,
      userInfoColor,
      step1Bg,
      getDemoPrizeBtn,
      step2Bg,
      step2ItemBg,
      step3Bg,
      ruleImage,
      bottomToTop,
  } = tState;

  const { operationType } = state.extra;
  const isView = operationType === 'view';
  const needDisable = isView;

  const handleUpdateField = (field: keyof ThemeState, value: any) => {
    tDispatch({ type: 'UPDATE_MODULE', payload: { [field]: value } });
  };
    return (
      <Box direction="row" spacing={32}>
        <Box direction="column" spacing={16} margin={[16, 0, 0, 0]}>
          <ImgUpload
            label="活动主图"
            required
            disabled={needDisable}
            img={{
            value: kv,
            width: 750,
          }}
            onSuccess={(url: string) => handleUpdateField('kv', url)}
            onReset={() => handleUpdateField('kv', -1)}
          />
          <ColorPickerFormItem
            disabled={needDisable}
            label="页面背景颜色"
            color={{ value: actBgColor }}
            onSetColor={(color: string) => handleUpdateField('actBgColor', color)}
            onReset={() => handleUpdateField('actBgColor', -1)}
          />
          <ImgUpload
            label="右上角按钮背景图"
            required
            img={{
              value: ruleBtn,
              width: 123,
              height: 45,
            }}
            disabled={needDisable}
            onSuccess={(url: string) => handleUpdateField('ruleBtn', url)}
            onReset={() => handleUpdateField('ruleBtn', -1)}
          />
          <ColorPickerFormItem
            disabled={needDisable}
            label="右上角按钮文字颜色"
            color={{ value: ruleBtnColor }}
            onSetColor={(color: string) => handleUpdateField('ruleBtnColor', color)}
            onReset={() => handleUpdateField('ruleBtnColor', -1)}
          />
        </Box>
        <span />
        <Box direction="column" spacing={16} margin={[16, 0, 0, 0]}>
          <ImgUpload
            label="用户信息及提示背景"
            required
            disabled={needDisable}
            img={{
                  value: userInfoBg,
                  width: 735,
                  height: 269,
              }}
            onSuccess={(url: string) => handleUpdateField('userInfoBg', url)}
            onReset={() => handleUpdateField('userInfoBg', -1)}
          />
          <ColorPickerFormItem
            disabled={needDisable}
            label="用户信息字体颜色"
            color={{ value: userInfoColor }}
            onSetColor={(color: string) => handleUpdateField('userInfoColor', color)}
            onReset={() => handleUpdateField('userInfoColor', -1)}
          />
          <ImgUpload
            disabled={needDisable}
            label="已购买的段数背景图"
            required
            img={{
                  value: step1Bg,
                  width: 718,
                  height: 544,
              }}
            onSuccess={(url: string) => handleUpdateField('step1Bg', url)}
            onReset={() => handleUpdateField('step1Bg', -1)}
          />
        </Box>
        <span />
        <Box direction="column" spacing={16} margin={[16, 0, 0, 0]}>
          <ImgUpload
            disabled={needDisable}
            label="中听区域背景图"
            required
            img={{
            value: step2Bg,
            width: 717,
          }}
            onSuccess={(url: string) => handleUpdateField('step2Bg', url)}
            onReset={() => handleUpdateField('step2Bg', -1)}
          />
          <ImgUpload
            disabled={needDisable}
            label="中听区域单个活动背景图"
            required
            img={{
                    value: step2ItemBg,
                    width: 691,
                    height: 399,
                }}
            onSuccess={(url: string) => handleUpdateField('step2ItemBg', url)}
            onReset={() => handleUpdateField('step2ItemBg', -1)}
          />
          <ImgUpload
            disabled={needDisable}
            label="中听奖品领取按钮背景图"
            required
            img={{
                  value: getDemoPrizeBtn,
                  width: 221,
                  height: 44,
                }}
            onSuccess={(url: string) => handleUpdateField('getDemoPrizeBtn', url)}
            onReset={() => handleUpdateField('getDemoPrizeBtn', -1)}
          />
        </Box>
        <span />
        <Box direction="column" spacing={16} margin={[16, 0, 0, 0]}>
          <ImgUpload
            disabled={needDisable}
            label="复购券及复购sku区域背景图"
            required
            img={{
                    value: step3Bg,
                    width: 715,
                    height: 1550,
                }}
            onSuccess={(url: string) => handleUpdateField('step3Bg', url)}
            onReset={() => handleUpdateField('step3Bg', -1)}
          />
          <ImgUpload
            disabled={needDisable}
            label="活动规则展示图"
            required
            img={{
                  value: ruleImage,
                  width: 715,
              }}
            onSuccess={(url: string) => handleUpdateField('ruleImage', url)}
            onReset={() => handleUpdateField('ruleImage', -1)}
          />
          <ImgUpload
            disabled={needDisable}
            label="返回顶部按钮"
            required
            img={{
                value: bottomToTop,
                width: 349,
                height: 86,
            }}
            onSuccess={(url: string) => handleUpdateField('bottomToTop', url)}
            onReset={() => handleUpdateField('bottomToTop', -1)}
          />
        </Box>
      </Box>
    );
}