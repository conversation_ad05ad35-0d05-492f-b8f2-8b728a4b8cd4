import { AppState } from '@/pages/activity/10202/create/type';

export const validateOrder = (state: AppState): string[] => {
  const errors: string[] = [];
  const { order } = state;

  if (!order.days || order.days <= 1) {
    errors.push('前置订单时间需大于1天');
  }
  if (order.isDelayedDisttributionBefore === 1 && (!order.awardDaysBefore || order.awardDaysBefore <= 0)) {
    errors.push('请设置前置订单延迟发奖天数');
  }
  if (!order.beforeSkuList || order.beforeSkuList.length === 0) {
    errors.push('请上传前置正装SKU数据');
  }
  return errors;
};

