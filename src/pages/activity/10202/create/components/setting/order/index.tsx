import Container from '@/components/Container';
import { Form, NumberPicker, Radio, Button, Message, Dialog, Table } from '@alifd/next';
import { useOrder } from './hooks';
import { useActivity } from '../../../reducer';
import { ACTIVITY_STATUS } from '@/utils/constant';
import { useState } from 'react';
import { downloadExcel } from '@/utils';
import ExcelImport from '@/pages/activity/10202/create/components/setting/components/ExcelImport';
import { skuTemplateBeforeExport } from '@/api/v10202';

const FormItem = Form.Item;
const RadioGroup = Radio.Group;

export default function Order() {
  const { order, updateOrder } = useOrder();
  const { state } = useActivity();
  const { operationType, activityStatus } = state.extra;
  const [skuVisible, setSkuVisible] = useState(false);

  const isCreate = operationType === 'add';
  const isCopy = operationType === 'copy';
  const isEdit = operationType === 'edit';
  const isView = operationType === 'view';
  const isEnded = activityStatus === ACTIVITY_STATUS.ENDED;
  const notStart = activityStatus === ACTIVITY_STATUS.NOT_STARTED;
  const needDisable = isEnded || isEdit && !notStart || isView;

  // 下载前置订单模板
  const downloadTemplate = async () => {
    try {
      const data: any = await skuTemplateBeforeExport();
      downloadExcel(data, '前置sku导入模板');
    } catch (error) {
      Message.error(error.message);
    }
  };

  // 将上传的前置sku数据设置到order的beforeSkuList中
  const setBeforeSkuList = (val) => {
    updateOrder({
      beforeSkuList: val,
    });
  };

  return (
    <Container title="前置订单设置">
      <Form>
        <FormItem label="前置订单时间" required disabled>
          近{' '}
          <NumberPicker
            min={1}
            max={180}
            type="inline"
            value={order.days}
            onChange={(days: number) => updateOrder({ days })}
          />{' '}
          天已购
        </FormItem>

        <FormItem label="前置正装SKU上传" required disabled={needDisable}>
          {(isCreate || isCopy || notStart) && (
            <>
              <Message type="notice" style={{ marginBottom: 10 }}>
                导入须知： <br />
                1.导入前请下载模板，将你要上传的数据放入模板中，使用模板进行导入。
                <br />
                2.单次导入最大5M，导入中请不要关闭此页面。
                <br />
                <Button text type="primary" onClick={downloadTemplate} disabled={needDisable}>
                  下载模板
                </Button>
              </Message>
              <ExcelImport
                buttonText="上传前置正装sku数据"
                action={`${process.env.ICE_BASE_URL || ''}/10202/importSkuExcelBefore`}
                onSuccess={(res) => {
                    const { response } = res;
                    // code 是 200 就保留文件名称，其他情况不展示文件名
                    if (response && response.code === 200) {
                      if (response.data) {
                        setBeforeSkuList(response.data);
                        Message.success('导入成功');
                        return true; // 返回 true 保留文件名
                      } else {
                        Message.error('导入数据为空，请检查文件内容');
                        return false; // 返回 false 不展示文件名
                      }
                    } else {
                      // code 不是 200 (比如 500)，不展示文件名
                      const errorMessage = response?.message || '导入失败，请检查数据格式';
                      Message.error(errorMessage);
                      return false; // 返回 false 不展示文件名
                    }
                  }}
                onError={(error) => {
                    console.error('上传失败：', error);
                    const errorMessage = error?.response?.message || error?.message;
                    Message.error(errorMessage);
                  }}
                onClear={() => {
                    // 清除文件时同时清空beforeSkuList
                    setBeforeSkuList([]);
                    console.log('已清除前置正装sku数据');
                  }}
              />
            </>
          )}
          {order.beforeSkuList.length > 0 && (
            <FormItem disabled={false}>
              <Button onClick={() => setSkuVisible(true)} style={{ marginTop: '10px' }}>
                查看正装SKU
              </Button>
            </FormItem>
          )}
        </FormItem>

        <FormItem label="奖品延迟发放" required disabled>
          <RadioGroup
            value={order.isDelayedDisttributionBefore}
            onChange={(isDelayedDisttributionBefore: number) =>
              updateOrder({ isDelayedDisttributionBefore })}
          >
            <Radio value={0}>否</Radio>
            <Radio value={1}>是</Radio>
          </RadioGroup>

          {order.isDelayedDisttributionBefore === 1 && (
            <div style={{ marginTop: 10 }}>
              延迟发放{' '}
              <NumberPicker
                disabled
                min={1}
                max={30}
                type="inline"
                value={order.awardDaysBefore}
                onChange={(awardDaysBefore: number) => updateOrder({ awardDaysBefore })}
              />{' '}
              天
            </div>
          )}
        </FormItem>
      </Form>

      <Dialog
        title="前置正装SKU列表"
        visible={skuVisible}
        footer={false}
        onClose={() => setSkuVisible(false)}
        style={{ width: 800 }}
      >
        <Table dataSource={order.beforeSkuList} style={{ margin: '10px' }}>
          <Table.Column title="skuId" dataIndex="skuId" />
          <Table.Column title="spuId" dataIndex="spuId" />
          <Table.Column title="SKU名称" dataIndex="skuName" />
          <Table.Column title="段数" dataIndex="sectionNum" />
          <Table.Column title="品线" dataIndex="goodsLine" />
          <Table.Column
            title="默认兜底图"
            dataIndex="skuMainPicture"
            cell={(value) => (
              <img src={value} style={{ height: 50 }} alt="" />
            )}
          />
        </Table>
      </Dialog>
    </Container>
  );
}