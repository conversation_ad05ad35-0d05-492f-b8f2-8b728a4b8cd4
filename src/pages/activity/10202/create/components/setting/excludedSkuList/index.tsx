import Container from '@/components/Container';
import { Form, Button, Message, Dialog, Table } from '@alifd/next';
import { useExcluded } from './hooks';
import { useActivity } from '../../../reducer';
import { ACTIVITY_STATUS } from '@/utils/constant';
import { useState } from 'react';
import { downloadExcel } from '@/utils';
import ExcelImport from '@/pages/activity/10202/create/components/setting/components/ExcelImport';
import { skuTemplateExcludedExport } from '@/api/v10202';

const FormItem = Form.Item;

export default function ExcludedSkuList() {
  const { excludedSkuList, updateExcluded } = useExcluded();
  const { state } = useActivity();
  const { operationType, activityStatus } = state.extra;
  const [skuVisible, setSkuVisible] = useState(false);

  const isCreate = operationType === 'add';
  const isCopy = operationType === 'copy';
  const isEdit = operationType === 'edit';
  const isView = operationType === 'view';
  const isEnded = activityStatus === ACTIVITY_STATUS.ENDED;
  const notStart = activityStatus === ACTIVITY_STATUS.NOT_STARTED;
  const needDisable = isEnded || isEdit && !notStart || isView;

  // 下载排除订单模板
  const downloadTemplate = async () => {
    try {
      const data: any = await skuTemplateExcludedExport();
      downloadExcel(data, '排除商品sku导入模板');
    } catch (error) {
      Message.error(error.message);
    }
  };

  // 将上传的排除sku数据设置到order的excludedSkuList中
  const setExcludedSkuList = (val: any[]) => {
    updateExcluded(
        {
          excludedSkuList: val,
        },
    );
  };

  return (
    <Container title="订单排除SKU设置">
      <Form>
        <FormItem label="排除SKU上传" required disabled={needDisable}>
          {(isCreate || isCopy || notStart) && (
            <>
              <Message type="notice" style={{ marginBottom: 10 }}>
                导入须知： <br />
                1.导入前请下载模板，将你要上传的数据放入模板中，使用模板进行导入。
                <br />
                2.单次导入最大5M，导入中请不要关闭此页面。
                <br />
                <Button text type="primary" onClick={downloadTemplate} disabled={needDisable}>
                  下载模板
                </Button>
              </Message>
              <ExcelImport
                buttonText="上传排除sku数据"
                action={`${process.env.ICE_BASE_URL || ''}/10202/importSkuExcelExcluded`}
                onSuccess={(res) => {
                    const { response } = res;
                    // code 是 200 就保留文件名称，其他情况不展示文件名
                    if (response && response.code === 200) {
                      if (response.data) {
                        setExcludedSkuList(response.data);
                        Message.success('导入成功');
                        return true; // 返回 true 保留文件名
                      } else {
                        Message.error('导入数据为空，请检查文件内容');
                        return false; // 返回 false 不展示文件名
                      }
                    } else {
                      // code 不是 200 (比如 500)，不展示文件名
                      const errorMessage = response?.message || '导入失败，请检查数据格式';
                      Message.error(errorMessage);
                      return false; // 返回 false 不展示文件名
                    }
                  }}
                onError={(error) => {
                    console.error('上传失败：', error);
                    const errorMessage = error?.response?.message || error?.message;
                    Message.error(errorMessage);
                  }}
                onClear={() => {
                    // 清除文件时同时清空excludedSkuList
                    setExcludedSkuList([]);
                    console.log('已清除排除正装sku数据');
                  }}
              />
            </>
          )}
          {excludedSkuList.length > 0 && (
            <FormItem disabled={false}>
              <Button onClick={() => setSkuVisible(true)} style={{ marginTop: '10px' }}>
                查看排除SKU
              </Button>
            </FormItem>
          )}
        </FormItem>
      </Form>

      <Dialog
        title="排除SKU"
        visible={skuVisible}
        footer={false}
        onClose={() => setSkuVisible(false)}
        style={{ width: 800 }}
      >
        <Table dataSource={excludedSkuList} style={{ margin: '10px' }}>
          <Table.Column title="skuId" dataIndex="skuId" />
        </Table>
      </Dialog>
    </Container>
  );
}