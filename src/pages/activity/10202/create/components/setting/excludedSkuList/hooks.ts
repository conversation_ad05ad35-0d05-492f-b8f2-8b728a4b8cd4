import { useActivity } from '@/pages/activity/10202/create/reducer';
import { validateExcludedSkuList } from './validator';
import { ModuleType } from '@/pages/activity/10202/create/type';

export function useExcluded() {
  const { state, dispatch } = useActivity();

  const updateExcluded = (data: { excludedSkuList: any[] }) => {
    dispatch({
      type: 'UPDATE_EXCLUDED_SKU_LIST',
      payload: data.excludedSkuList,
    });

    // 清除错误
    dispatch({
      type: 'CLEAR_ERRORS',
      module: ModuleType.EXCLUDED_SKU_LIST,
    });
  };

  const validateExcludedData = () => {
    const errors = validateExcludedSkuList(state);
    dispatch({
      type: 'VALIDATE_MODULE',
      module: ModuleType.EXCLUDED_SKU_LIST,
      payload: errors,
    });
    return errors.length === 0;
  };

  return {
    excludedSkuList: state.excludedSkuList,
    updateExcluded,
    validateExcluded: validateExcludedData,
    errors: state.errors[ModuleType.EXCLUDED_SKU_LIST],
  };
}