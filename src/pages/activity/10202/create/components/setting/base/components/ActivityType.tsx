import HelpTooltip from '@/components/HelpTooltip/HelpTooltip';
import { Form, Radio } from '@alifd/next';
import { useBaseInfo } from '../hooks';
import { useActivity } from '../../../../reducer';

export default function ActivityType() {
  const { baseInfo, updateBaseInfo } = useBaseInfo();
  const { state, dispatch } = useActivity();
  const { wheelType } = baseInfo;

  // 获取当前操作状态
  const { operationType } = state.extra;
  // 是否编辑活动
  const isEdit = operationType === 'edit';
  const isCopy = operationType === 'copy';

  const handleChange = (value: number) => {
    updateBaseInfo({ wheelType: value });
    if (isCopy || isEdit) {
      dispatch({
        type: 'UPDATE_DECORATE',
        payload: '{}',
      });
    }
  };

  const imgBallon = (src: string) => {
    return (
      <div style={{ width: 150, height: 300, overflow: 'scroll' }}>
        <img style={{ width: '100%' }} src={src} alt={''} />
      </div>
    );
  };
  return (
    <Form.Item label="抽奖类型" required>
      <Radio.Group value={wheelType} onChange={handleChange}>
        <Radio value={1}>大转盘 <HelpTooltip align="r" content={imgBallon('https://novae.oss-cn-zhangjiakou.aliyuncs.com/vf/%E5%A4%A7%E8%BD%AC%E7%9B%98%E8%A3%85%E4%BF%AE/VF%E5%A4%A7%E8%BD%AC%E7%9B%98.jpg')} /></Radio>
        <Radio value={2}>抽盲盒 <HelpTooltip align="r" content={imgBallon('https://novae.oss-cn-zhangjiakou.aliyuncs.com/vf/%E7%9B%B2%E7%9B%92%E8%A3%85%E4%BF%AE/VF%E6%8A%BD%E7%9B%B2%E7%9B%92.png')} /></Radio>
      </Radio.Group>
      <div className="form-extra">注：切换抽奖类型需重新配置装修样式</div>
    </Form.Item>
  );
}