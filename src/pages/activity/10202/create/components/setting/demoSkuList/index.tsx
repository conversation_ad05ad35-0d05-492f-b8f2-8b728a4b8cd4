import Container from '@/components/Container';
import { Button, Dialog, Form, Table, Message } from '@alifd/next';
import { useActivity } from '../../../reducer';
import { DemoSkuListState, ModuleType } from '../../../../create/type';
import { useEffect, useState } from 'react';
import { downloadExcel, getPrizeTypeLabel, PrizeTypeEnum } from '@/utils';
import ExcelImport from '../components/ExcelImport';
import { ACTIVITY_STATUS } from '@/utils/constant';
import { useDemoSkuList } from '../demoSkuList/hooks';
import choosePrize from './components/choosePrize/choosePrize';
import editPrize from './components/editPrize';
import { activityCustomizeActivityMinusLotteryNum } from '@/api/b';
import { skuTemplateDemoExport } from '@/api/v10202';

const FormItem = Form.Item;

// 初始化奖品项
const initPrizeItem = {
  lotteryName: '',
  lotteryType: PrizeTypeEnum.THANKS.value,
  lotteryValue: '',
  prizeNum: '',
  dayLimitType: '',
  awardLimitType: '',
  awardLimitCount: '',
};

// 警告内容组件
const WarningContent = () => {
  return (
    <div style={{ textAlign: 'center' }}>
      <div>编辑奖品将释放已冻结库存</div>
      <div><span style={{ color: 'red' }}>请务必重新完整保存活动</span>，是否继续</div>
    </div>
  );
};

export default function DemoSkuList() {
  const { state, dispatch } = useActivity();
  const { demoSkuList, updateDemoSkuList } = useDemoSkuList();
  const { operationType, activityStatus } = state.extra;

  const isCreate = operationType === 'add';
  const isCopy = operationType === 'copy';
  const isEdit = operationType === 'edit';
  const isView = operationType === 'view';
  const isEnded = activityStatus === ACTIVITY_STATUS.ENDED;
  const notStart = activityStatus === ACTIVITY_STATUS.NOT_STARTED;
  const needDisable = isEnded || isEdit && !notStart || isView;

  const [expandedTableData, setExpandedTableData] = useState<any>([]);

  const updateList = (list: DemoSkuListState[]) => {
    dispatch({ type: 'UPDATE_DEMO_SKU_LIST', payload: list });
    dispatch({ type: 'CLEAR_ERRORS', module: ModuleType.DEMO_SKU_LIST });
  };

  // 回滚库存函数
  const handleBackStock = async (record: any) => {
    try {
      // 只有实物奖品且有prizeId时才需要释放库存
      if (record.lotteryType === PrizeTypeEnum.PRACTICALITY.value && record.prizeId) {
        await activityCustomizeActivityMinusLotteryNum({
          lotteryId: record.prizeId,
          num: record.prizeNum,
        });
      }
    } catch (error) {
      console.error('回撤库存失败：', error);
      throw error; // 重新抛出错误，让调用方知道释放库存失败
    }
  };

  // 下载中听模板
  const downloadDemoTemplate = async () => {
    try {
      const data: any = await skuTemplateDemoExport();
      downloadExcel(data, '中听导入模板');
    } catch (error) {
      Message.error(error.message);
    }
  };

  // 设置demoSkuList
  const setDemoSectionList = (data: any) => {
    const newDemoSkuList: any = [];
    data.forEach((sectionItem: any) => {
      sectionItem.skuInfo.forEach((skuItem: any) => {
        newDemoSkuList.push({
          goodsLine: skuItem.goodsLine,
          // sectionSort: skuItem.sectionNum,
          sectionNum: skuItem.sectionNum,
          skuMainPicture: skuItem.skuMainPicture,
          prizeMainName: skuItem.prizeMainName,
          skuName: skuItem.skuName,
          skuId: skuItem.skuId,
          skuDayLimitCount: skuItem.dayLimitCount,
          prizeList: [],
        });
      });
    });
    updateDemoSkuList(newDemoSkuList); // 使用 updateDemoSkuList 而不是 updateList
  };

  // 编辑/添加奖品
  const editPrizeHandler = async (skuIndex, index: number, record: any) => {
    console.log('编辑奖品', record.prize.prizeId, skuIndex, index, record);

    // 字段名映射：将表格中的字段名映射为编辑组件期望的字段名
    const mappedRecord = {
      ...record,
    };
    const isPhysical = mappedRecord.lotteryType === PrizeTypeEnum.PRACTICALITY.value;
    const needBackStock = record.prizeId && isPhysical;
    const isNewPrize = record.isAddNew === true; // 新添加的奖品

    // 封装编辑奖品的核心逻辑
    const handleEdit = async () => {
      try {
        let result: any;
        if (record.lotteryName === '' || record.lotteryName === '谢谢参与') {
          result = await choosePrize({
            activityType: 'prizeAndSku',
            disabledTabs: [6, 7, 9],
            status: [activityStatus, operationType],
            needDisable: false,
            needShowImg: false,
          });
        } else {
          // 已有奖品（包括谢谢惠顾类型）都使用editPrize，传入映射后的记录
          result = await editPrize({
            editPrizeInfo: mappedRecord,
            disabledTabs: [6, 7, 9], // 可根据需要配置禁用的奖品类型
            field: null, // 如果需要表单字段验证，可以传入field
            activityType: 'prizeAndSku',
            status: [activityStatus, operationType],
            needShowImg: false,
          });
        }

        if (result) {
          console.log('编辑奖品结果：', result);
          // 将编辑结果的字段名映射回表格期望的字段名
          const mappedResult = {
            potNum: result.potNum !== undefined ? result.potNum : record.potNum,
            lotteryName: result.lotteryName || record.lotteryName,
            lotteryType: result.lotteryType || record.lotteryType,
            lotteryValue: result.lotteryValue || record.lotteryValue,
            prizeNum: result.prizeNum || record.prizeNum,
            dayLimitType: result.dayLimitType || record.dayLimitType,
            awardLimitType: result.awardLimitType || record.awardLimitType,
            awardLimitCount: result.awardLimitCount || record.awardLimitCount,
            showImage: result.showImage || record.showImage,
            isAddNew: result.isAddNew !== undefined ? result.isAddNew : record.isAddNew,
            price: result.price || record.price,
            sortId: result.sortId !== undefined ? result.sortId : record.sortId,
            status: result.status !== undefined ? result.status : (record.status !== undefined ? record.status : 1),
            prizeId: record.prize.prizeId ? record.prize.prizeId : '',
          };

          // 更新demoSkuList中对应SKU的奖品
          const newDemoSkuList = [...demoSkuList];
          newDemoSkuList[skuIndex].prizeList[index] = mappedResult;
          updateDemoSkuList(newDemoSkuList);
          Message.success('编辑奖品成功');
        }
      } catch (error) {
        console.error('编辑奖品失败：', error);
        Message.error(error.message);
      }
    };

    // 针对已开始活动且有ID的奖品且不是谢谢惠顾类型，需要先确认
    if (needBackStock) {
      Dialog.confirm({
        title: '提示',
        content: <WarningContent />,
        onOk: async () => {
          // 先将列表中对应奖品重置为谢谢参与
          const newDemoSkuList = [...demoSkuList];
          newDemoSkuList[skuIndex].prizeList[index] = { ...initPrizeItem };
          updateDemoSkuList(newDemoSkuList);
          // 回滚库存
          await handleBackStock(record);
          // 然后执行编辑逻辑
          await handleEdit();
        },
      });
    } else {
      // 其他情况直接编辑
      await handleEdit();
    }
  };

  // 添加奖品到指定SKU
  const addPrizeToSku = async (skuIndex: number) => {
    const newDemoSkuList = [...demoSkuList];
    if (!newDemoSkuList[skuIndex].prizeList) {
      newDemoSkuList[skuIndex].prizeList = [];
    }

    // 检查是否已达到最大奖品数量（3个）
    if (newDemoSkuList[skuIndex].prizeList.length >= 3) {
      return;
    }
    const res = await choosePrize({
      activityType: 'prizeAndSku',
      disabledTabs: [6, 7, 9],
      status: [activityStatus, operationType],
      needDisable: false,
      needShowImg: false,
    });

    // 只有当用户选择了奖品时才添加到列表中
    if (res) {
      newDemoSkuList[skuIndex].prizeList.push(res);
      updateDemoSkuList(newDemoSkuList);
    }
  };

  // 删除SKU行
  const deleteSku = (skuIndex: number) => {
    const newDemoSkuList = [...demoSkuList]; // 创建新数组副本
    newDemoSkuList.splice(skuIndex, 1);
    updateDemoSkuList(newDemoSkuList);
  };

  const deletePrize = (skuIndex: number, prizeIndex: number) => {
    const handleRemove = async () => {
      const list = [...demoSkuList];
      list[skuIndex].prizeList.splice(prizeIndex, 1);
      updateList(list);
    };

    const content = '确定要将此奖品删除吗？此次操作不可恢复';
    Dialog.confirm({
      title: '删除奖品',
      content,
      onOk: handleRemove,
    });
  };

  // 生成展开的表格数据，每个SKU的每个奖品占一行
  const generateExpandedTableData = () => {
    const expandedData: any = [];

    if (!Array.isArray(demoSkuList) || demoSkuList.length === 0) {
      return expandedData;
    }

    demoSkuList.forEach((sku, skuIndex) => {
      const prizeList = sku.prizeList || [];

      if (prizeList.length === 0) {
        expandedData.push({
          ...sku,
          skuIndex,
          prizeIndex: -1,
          prize: null,
          isFirstPrizeRow: true,
          prizeRowCount: 1,
          sortId: skuIndex,
        });
      } else {
        prizeList.forEach((prize, prizeIndex) => {
          expandedData.push({
            ...sku,
            skuIndex,
            prizeIndex,
            prize,
            // 将奖品字段提升到根级别，方便表格显示
            lotteryName: prize.lotteryName,
            lotteryType: prize.lotteryType,
            lotteryValue: prize.lotteryValue,
            prizeNum: prize.prizeNum,
            price: prize.price,
            dayLimitType: prize.dayLimitType,
            skuDayLimitCount: sku.skuDayLimitCount,
            awardLimitType: prize.awardLimitType,
            awardLimitCount: prize.awardLimitCount,
            showImage: prize.showImage,
            isFirstPrizeRow: prizeIndex === 0,
            prizeRowCount: prizeList.length,
            sortId: skuIndex,
          });
        });
      }
    });

    return expandedData;
  };

  // 监听 demoSkuList 变化，自动更新表格数据
  useEffect(() => {
    const expandedData = generateExpandedTableData();
    setExpandedTableData(expandedData);
  }, [demoSkuList]);

  return (
    <Container title="中听商品及奖品设置">
      <Form>
        {(isCreate || isCopy || notStart) && (
          <FormItem label="中听sku数据源上传">
            <>
              <Message type="notice" style={{ marginBottom: 10 }}>
                导入须知： <br />
                1.导入前请下载模板，将你要上传的数据放入模板中，使用模板进行导入。
                <br />
                2.单次导入最大5M，导入中请不要关闭此页面。
                <br />
                <Button text type="primary" onClick={downloadDemoTemplate} disabled={needDisable}>
                  下载模板
                </Button>
              </Message>
              <ExcelImport
                buttonText="上传中听sku数据"
                action={`${process.env.ICE_BASE_URL || ''}/10202/importSkuExcelDemo`}
                onSuccess={(res) => {
                  const { response } = res;
                  // code 是 200 就保留文件名称，其他情况不展示文件名
                  if (response && response.code === 200) {
                    if (response.data) {
                      setDemoSectionList(response.data);
                      Message.success('导入成功');
                      return true; // 返回 true 保留文件名
                    } else {
                      Message.error('导入数据为空，请检查文件内容');
                      return false; // 返回 false 不展示文件名
                    }
                  } else {
                    // code 不是 200 (比如 500)，不展示文件名
                    const errorMessage = response?.message || '导入失败，请检查数据格式';
                    Message.error(errorMessage);
                    return false; // 返回 false 不展示文件名
                  }
                }}
                onError={(error) => {
                  console.error('上传失败：', error);
                  const errorMessage = error?.response?.message || error?.message;
                  Message.error(errorMessage);
                }}
                onClear={() => {
                  setDemoSectionList([]);
                  console.log('已清除中听sku数据');
                }}
              />
            </>
          </FormItem>
        )}
        <FormItem label="中听奖品设置" required>
          <div style={{ overflowX: 'auto' }}>
            {/* { */}
            {/*  expandedTableData.length > 0 && ( */}
            <Table
              dataSource={expandedTableData}
              style={{ marginTop: '15px' }}
              cellProps={(rowIndex, colIndex, dataIndex, record) => {
                  // 需要合并的列：段数、品线、sku名称、skuId、sku图、奖品名、每日限额、SKU操作
                  const mergeColumns = [
                    'sectionNum',
                    'goodsLine',
                    'skuName',
                    'skuId',
                    'skuMainPicture',
                    'prizeMainName',
                    'skuDayLimitCount',
                    'skuOperation',
                  ];

                  if (mergeColumns.includes(dataIndex)) {
                    // 如果是该SKU的第一行奖品，显示合并的单元格
                    if (record.isFirstPrizeRow) {
                      return {
                        rowSpan: record.prizeRowCount,
                      };
                    } else {
                      // 其他行隐藏
                      return {
                        rowSpan: 0,
                      };
                    }
                  }

                  // 奖品相关列不合并
                  return {};
                }}
            >
              <Table.Column
                title="段数"
                dataIndex="sectionNum"
                cell={(value, index) => {
                    return (
                      <div>{value || '--'}</div>
                    );
                  }}
              />
              <Table.Column
                title="品线"
                dataIndex="goodsLine"
                cell={(value, index) => {
                    return (
                      <div>{value || '--'}</div>
                    );
                  }}
              />
              <Table.Column
                title="SKU名称"
                dataIndex="skuName"
                cell={(value, index) => {
                    return (
                      <div>{value || '--'}</div>
                    );
                  }}
              />
              <Table.Column
                title="SKU ID"
                dataIndex="skuId"
                cell={(value, index) => {
                    return (
                      <div>{value || '--'}</div>
                    );
                  }}
              />
              <Table.Column
                title="sku图"
                dataIndex="skuMainPicture"
                cell={(value, index) => {
                    return (
                      <img style={{ height: '60px' }} src={value} alt="" />
                    );
                  }}
              />
              <Table.Column
                title="每日限额"
                dataIndex="skuDayLimitCount"
                cell={(value, index) => {
                    return (
                      <div>{value || '--'}</div>
                    );
                  }}
              />
              <Table.Column
                title="奖品名"
                dataIndex="prizeMainName"
                cell={(value, index) => {
                    return (
                      <div>{value || '--'}</div>
                    );
                  }}
              />
              <Table.Column
                title="奖品"
                cell={(value, index, record) => {
                  if (record.prizeIndex === -1) return '-';
                  return record.lotteryName || '-';
                }}
              />
              <Table.Column
                title="奖品类型"
                cell={(value, index, record) => {
                  if (record.prizeIndex === -1) return '-';
                  return getPrizeTypeLabel(record.lotteryType);
                }}
              />
              <Table.Column
                title="单位数量"
                cell={(value, index, record) => {
                    if (record.prizeIndex === -1 || !record.lotteryType) return '-';
                    if (record.lotteryType === PrizeTypeEnum.MEMBER_POINT.value) {
                      return record.lotteryValue;
                    } else {
                      return 1;
                    }
                  }}
              />
              <Table.Column
                title="发放份数"
                cell={(value, index, record) => {
                  if (record.prizeIndex === -1) return '-';
                  return record.prizeNum || '-';
                }}
              />
              <Table.Column
                title="奖品行操作"
                cell={(_, index, row) => {
                  if (row.prizeIndex !== -1) {
                    return (
                      <div style={{ display: 'flex', flexDirection: 'column' }}>
                        <Button
                          style={{ margin: '0 auto' }}
                          type="primary"
                          size="small"
                          onClick={() => editPrizeHandler(row.skuIndex, row.prizeIndex, row)}
                        >
                          编辑奖品
                        </Button>
                        <Button
                          style={{ margin: '10px auto 0' }}
                          type="normal"
                          size="small"
                          disabled={needDisable}
                          onClick={() => deletePrize(row.skuIndex, row.prizeIndex)}
                        >
                          删除奖品
                        </Button>
                      </div>
                    );
                  } else {
                    return null;
                  }
                }}
              />
              <Table.Column
                title="SKU行操作"
                dataIndex="skuOperation"
                cell={(_, index, row) => {
                  const currentSku = demoSkuList[row.skuIndex];
                  const prizeCount = currentSku?.prizeList ? currentSku.prizeList.length : 0;

                  // 如果当前SKU不存在，返回空内容
                  if (!currentSku) {
                    return null;
                  }

                  return (
                    <div style={{ display: 'flex', flexDirection: 'column' }}>
                      <Button
                        type="secondary"
                        size="small"
                        onClick={() => addPrizeToSku(row.skuIndex)}
                        disabled={prizeCount >= 3 || needDisable}
                      >
                        添加奖品
                      </Button>
                      <Button
                        type="normal"
                        size="small"
                        style={{ margin: '10px auto 0' }}
                        onClick={() => deleteSku(row.skuIndex)}
                        disabled={demoSkuList.length === 1 || needDisable}
                      >
                        删除SKU
                      </Button>
                    </div>
                  );
                }}
              />
            </Table>
            {/* ) */}
            {/* } */}
          </div>
        </FormItem>
      </Form>

    </Container>
  );
}

