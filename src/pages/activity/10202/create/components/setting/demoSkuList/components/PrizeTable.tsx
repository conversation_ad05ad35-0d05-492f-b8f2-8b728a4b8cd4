import { Box, Button, Dialog, Table, Message } from '@alifd/next';
import { useState, useEffect } from 'react';
import editPrize from './editPrize';
import choosePrize from '../components/choosePrize/choosePrize';
import { getPrizeTypeLabel, PrizeTypeEnum } from '@/utils';
import { ACTIVITY_STATUS } from '@/utils/constant';
import { activityCustomizeActivityMinusLotteryNum } from '@/api/b';
const initPrizeItem = {
    lotteryName: '',
    lotteryType: PrizeTypeEnum.THANKS.value,
    lotteryValue: '',
    prizeNum: '',
    dayLimitType: '',
    awardLimitType: '',
    awardLimitCount: '',
};
interface Props {
    prizeList: any[];
    onPrizeChange?: (newPrizeList: any[]) => void;
    status: [number, string];
    disabledTabs: number[];
    activityId?: string;
}
const WarningContent = () => {
    return (
      <Box direction={'column'} spacing={10} align={'center'}>
        <div>编辑奖品将释放已冻结库存</div>
        <div><span className="text-red">请务必重新完整保存活动</span>，是否继续</div>
      </Box>
    );
};

// 奖品表格
export default function PrizeTable({ prizeList, onPrizeChange, status, disabledTabs, activityId }: Props) {
    const [activityStatus, operationType] = status;
    console.log('operationType', operationType);
    const [tableList, setTableList] = useState<any[]>(prizeList);
    const isNoStart = activityStatus === ACTIVITY_STATUS.NOT_STARTED;
    const isEnded = activityStatus === ACTIVITY_STATUS.ENDED;
    const isView = operationType === 'view';
    const isEdit = operationType === 'edit';

    // 同步外部prizeList的变化
    useEffect(() => {
        setTableList(prizeList);
        // // 编辑活动时，给每一个奖品找到对应的prizeId
        // if (isEdit) {
        //     getActivityPrizeId();
        // }
    }, [prizeList]);

    // 更新奖品列表并通知父组件
    const updatePrizeList = (newList: any[]) => {
        setTableList(newList);
        onPrizeChange?.(newList);
    };

    const handleBackStock = async (record: any) => {
        try {
            // 只有实物奖品且有prizeId时才需要释放库存
            if (record.lotteryType === PrizeTypeEnum.PRACTICALITY.value && record.prizeId) {
                await activityCustomizeActivityMinusLotteryNum({
                    lotteryId: record.prizeId,
                    num: record.prizeNum,
                });
            }
        } catch (error) {
            console.error('回撤库存失败：', error);
            throw error; // 重新抛出错误，让调用方知道释放库存失败
        }
    };

    // 编辑奖品
    const editPrizeHandler = async (index: number, record: any) => {
        console.log('编辑奖品', record);

        // 字段名映射：将表格中的字段名映射为编辑组件期望的字段名
        const mappedRecord = {
            ...record,
        };

        const isThanks = mappedRecord.lotteryType === PrizeTypeEnum.THANKS.value;
        const isPhysical = mappedRecord.lotteryType === PrizeTypeEnum.PRACTICALITY.value;
        const needBackStock = record.prizeId && isPhysical;
        const isNewPrize = record.isAddNew === true; // 新添加的奖品

        // 封装编辑奖品的核心逻辑
        const handleEdit = async () => {
            try {
                let result: any;
                // 只有新添加的奖品才调用choosePrize让用户选择奖品
                if (isNewPrize && (record.lotteryName === '' || record.lotteryName === '谢谢参与')) {
                    result = await choosePrize({
                        activityType: 'prizeAndSku',
                        disabledTabs,
                        status,
                        needDisable: false,
                    });
                } else {
                    // 已有奖品（包括谢谢惠顾类型）都使用editPrize，传入映射后的记录
                    result = await editPrize({
                        editPrizeInfo: mappedRecord,
                        disabledTabs, // 可根据需要配置禁用的奖品类型
                        field: null, // 如果需要表单字段验证，可以传入field
                        activityType: 'prizeAndSku',
                        status,
                        needShowImg: true,
                    });
                }

                if (result) {
                    console.log('编辑奖品结果：', result);
                    // 将编辑结果的字段名映射回表格期望的字段名
                    const mappedResult = {
                        potNum: result.potNum !== undefined ? result.potNum : record.potNum,
                        lotteryName: result.lotteryName || record.lotteryName,
                        lotteryType: result.lotteryType || record.lotteryType,
                        lotteryValue: result.lotteryValue || record.lotteryValue,
                        prizeNum: result.prizeNum || record.prizeNum,
                        dayLimitType: result.dayLimitType || record.dayLimitType,
                        awardLimitType: result.awardLimitType || record.awardLimitType,
                        awardLimitCount: result.awardLimitCount || record.awardLimitCount,
                        showImage: result.showImage || record.showImage,
                        isAddNew: result.isAddNew !== undefined ? result.isAddNew : record.isAddNew,
                        price: result.price || record.price,
                        sortId: result.sortId !== undefined ? result.sortId : record.sortId,
                        status: result.status !== undefined ? result.status : (record.status !== undefined ? record.status : 1),
                    };

                    const newList = [...tableList];
                    newList[index] = mappedResult;
                    updatePrizeList(newList);
                    Message.success('编辑奖品成功');
                }
            } catch (error) {
                console.error('编辑奖品失败：', error);
                Message.error(error.message);
            }
        };

        // 针对已开始活动且有ID的奖品且不是谢谢惠顾类型，需要先确认
        if (needBackStock) {
            Dialog.confirm({
                title: '提示',
                content: <WarningContent />,
                onOk: async () => {
                    // 先将列表中对应奖品重置为谢谢参与
                    const newList = [...tableList];
                    newList[index] = { ...initPrizeItem };
                    updatePrizeList(newList);
                    // 回滚库存
                    await handleBackStock(record);
                    // 然后执行编辑逻辑
                    await handleEdit();
                },
            });
        } else {
            // 其他情况直接编辑
            await handleEdit();
        }
    };

    // 下架奖品
    const offShelfPrize = async (index: number, record: any) => {
        // 检查是否是编辑进行中的活动
        const isEditingRunningActivity = isEdit && activityStatus !== ACTIVITY_STATUS.NOT_STARTED;

        if (isEditingRunningActivity) {
            // 检查下架后是否会导致该阶梯只有下架奖品
            const currentOnShelfPrizes = tableList.filter(prize => prize.status === 1);
            if (currentOnShelfPrizes.length === 1 && record.status === 1) {
                Message.error('不能下架最后一个上架奖品，阶梯下至少需要保留一个上架奖品');
                return;
            }
        }

        Dialog.confirm({
            title: '下架奖品',
            content: '确认要下架此奖品吗？',
            onOk: () => {
                try {
                    const newList = [...tableList];
                    newList[index] = {
                        ...record,
                        status: 0, // 设置为下架状态
                    };
                    updatePrizeList(newList);
                    Message.success('奖品下架成功');
                } catch (error) {
                    console.error('下架奖品失败：', error);
                    Message.error('下架奖品失败');
                }
            },
        });
    };

    // 重置奖品为空
    const removePrize = async (index: number, record: any) => {
        const hasId = record.prizeId || record.prizeKey;
        // 检查是否是编辑进行中的活动
        const isEditingRunningActivity = isEdit && activityStatus !== ACTIVITY_STATUS.NOT_STARTED;

        if (isEditingRunningActivity && record.status === 1) {
            // 检查删除后是否会导致该阶梯只有下架奖品
            const currentOnShelfPrizes = tableList.filter(prize => prize.status === 1);
            if (currentOnShelfPrizes.length === 1) {
                Message.error('不能删除最后一个上架奖品，阶梯下至少需要保留一个上架奖品');
                return;
            }
        }

        // 封装删除奖品的核心逻辑
        const handleRemove = async () => {
            try {
                // 如果奖品有ID（已保存过的奖品），需要调用释放库存接口
                if (hasId) {
                    await handleBackStock(record);
                }
                const newList = [...tableList];
                // 真正删除奖品：从数组中移除该项
                newList.splice(index, 1);
                // 重新调整剩余奖品的sortId，确保连续性
                const reorderedList = newList.map((prize, idx) => ({
                    ...prize,
                    sortId: idx + 1,
                }));
                updatePrizeList(reorderedList);
                Message.success('奖品删除成功');
            } catch (error) {
                console.error('删除奖品失败：', error);
                Message.error('删除奖品失败');
            }
        };

        // 如果奖品有ID，需要提醒用户删除将释放已冻结库存
        const content = hasId
            ? '删除奖品将释放已冻结库存，请务必重新完整保存活动，是否继续？'
            : '确定要将此奖品删除吗？此次操作不可恢复';

        Dialog.confirm({
            title: '删除奖品',
            content,
            onOk: handleRemove,
        });
    };

    // 渲染操作按钮
    const renderAction = (_: boolean, index: number, record: any) => {
        const isCreate = operationType === 'add';
        const isCopy = operationType === 'copy';
        // 新添加的奖品
        const isNew = record.isAddNew;
        // 上架
        const isPutaway = record.status === 1;
        // 活动未开始
        const isNoStart = activityStatus === ACTIVITY_STATUS.NOT_STARTED;

        // 编辑按钮显示逻辑：活动进行中编辑且不是谢谢参与时展示 或 活动创建/复制/编辑新添加的奖品时展示
        const showEditButton = isCreate || isCopy || isNew || isPutaway;

        // 其他按钮显示逻辑：仅在活动创建、复制和编辑未开始活动时展示,开始的活动，当奖品行为空的时候展示删除按钮
        const showOtherButton = isCreate || isCopy || isNoStart || isNew || (!isNoStart && (!record.lotteryName || record.lotteryName === ''));

        return (
          <Box direction="row" align="center" spacing={10}>
            {showEditButton && (
            <Button type={'primary'} text onClick={() => editPrizeHandler(index, record)}>编辑</Button>
            )}
            {showOtherButton && (
            <Box direction="row" align="center" spacing={10}>
              <Button type={'primary'} text onClick={() => removePrize(index, record)}>删除</Button>
            </Box>
            )}
            {/* 下架按钮：只有上架且不是新添加状态的奖品，且已开始的活动才显示 */}
            {isPutaway && !isNew && !isNoStart && (
              <Button type={'primary'} text onClick={() => offShelfPrize(index, record)}>下架</Button>
            )}
          </Box>
        );
    };

    return (
      <Table dataSource={tableList}>
        <Table.Column title="奖项" cell={(_, index) => index + 1} />
        <Table.Column title="奖品名称" dataIndex="lotteryName" cell={(value) => value || '-'} />
        <Table.Column
          title="奖品类型"
          dataIndex="lotteryType"
          cell={(value) => getPrizeTypeLabel(value)}
        />
        <Table.Column
          title="单位数量"
          cell={(value, index, record) => {
                    if (!record.lotteryType) return '-';
                    if (record.lotteryType === PrizeTypeEnum.MEMBER_POINT.value) {
                        return record.lotteryValue;
                    } else {
                        return 1;
                    }
                }}
        />
        <Table.Column title="发放份数" dataIndex="prizeNum" cell={value => value || '-'} />
        <Table.Column
          title="奖品图"
          dataIndex="showImage"
          cell={(value) => {
                    if (value) {
                        return <img src={value} alt="奖品" style={{ width: 50, height: 50, objectFit: 'cover' }} />;
                    } else {
                        return '-';
                    }
                }}
        />
        <Table.Column title="状态" dataIndex="status" cell={(value) => (value === 1 ? '上架' : '下架')} />
        {
                (!isEnded && !isView) && <Table.Column title="操作" cell={renderAction} />
            }
      </Table>
    );
}
