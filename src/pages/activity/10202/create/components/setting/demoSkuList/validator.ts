import { AppState } from '@/pages/activity/10202/create/type';

export const validateDemoSkuList = (state: AppState): string[] => {
  const errors: string[] = [];
  const { demoSkuList, base } = state as any;

  if (!demoSkuList || demoSkuList.length === 0) {
    errors.push('请添加至少一个中听SKU');
    return errors;
  }

  for (let i = 0; i < demoSkuList.length; i++) {
    const sku = demoSkuList[i] as any;
    if (!sku.skuId) {
      errors.push(`中听奖品第${i + 1}个SKU的skuId不能为空`);
    }
    if (!sku.skuName) {
      errors.push(`中听奖品第${i + 1}个SKU的商品名称不能为空`);
    }
    if (!sku.goodsLine) {
      errors.push(`中听奖品第${i + 1}个SKU的品线不能为空`);
    }
    if (!sku.sectionNum) {
      errors.push(`中听奖品第${i + 1}个SKU的段数不能为空`);
    }
    if (!sku.skuDayLimitCount || Number(sku.skuDayLimitCount) === 0) {
      errors.push(`中听奖品第${i + 1}个SKU的每日限额必须大于0`);
    }
    const prizeList = sku.prizeList || [];
    if (prizeList.length === 0) {
      errors.push(`中听奖品第${i + 1}个SKU至少需要配置一个奖品`);
    }
    if (prizeList.length > 3) {
      errors.push(`中听奖品第${i + 1}个SKU最多只能配置3个奖品`);
    }
  }

  return errors;
};

