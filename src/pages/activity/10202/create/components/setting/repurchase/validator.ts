import { AppState } from '@/pages/activity/10202/create/type';
import dayjs from 'dayjs';

export const validateRepurchase = (state: AppState): string[] => {
  const errors: string[] = [];
  const { repurchase, base } = state as any;
  if (!repurchase.repurchasePrizeList || repurchase.repurchasePrizeList.length === 0) {
    errors.push('请设置复购奖品');
    return errors;
  }
  repurchase.repurchasePrizeList.forEach((prize: any, i: number) => {
    if (!prize.lotteryName) {
      errors.push(`请设置第${i + 1}个复购奖品`);
    }
    if (!prize.sectionNum) {
      errors.push(`请设置第${i + 1}个复购奖品的段数`);
    }
    if (!prize.goodsLine) {
      errors.push(`请设置第${i + 1}个复购奖品的品线`);
    }
    if (prize.startDate && prize.endDate) {
      const ps = dayjs(prize.startDate);
      const pe = dayjs(prize.endDate);
      const as = dayjs(base.startTime);
      const ae = dayjs(base.endTime);
      if (ps.isAfter(as)) {
        errors.push(`第${i + 1}个复购奖品开始时间必须小于等于活动开始时间`);
      }
      if (pe.isBefore(ae)) {
        errors.push(`第${i + 1}个复购奖品结束时间必须大于等于活动结束时间`);
      }
    }
  });
  if (!repurchase.repurchaseSkuList || repurchase.repurchaseSkuList.length === 0) {
    errors.push('请设置复购商品');
  }
  return errors;
};

