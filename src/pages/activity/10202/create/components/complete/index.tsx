import Container from '@/components/Container';
import { QRCodeSVG } from 'qrcode.react';
import { Box, Button } from '@alifd/next';
import { history } from '@ice/runtime';
import { useActivity } from '../../reducer';
import styles from './index.module.scss';

export default function Complete() {
  const { state } = useActivity();
  const { activityUrl } = state.extra;

  return (
    <div className={styles.complete}>
      <Container>
        <div className={styles['qr-img-container']}>
          <QRCodeSVG
            value={activityUrl || ''}
            className="qr-img"
          />
          <div className={styles['qr-img-des']}>
            <p className={styles['qr-img-message']}>{'活动保存成功'}</p>
            <p className={[styles['qr-img-tips'], 'text-red'].join(' ')}>
              重要提示：您的活动需投放，活动才能透出哦~
            </p>
            <p className={styles['qr-img-use-taobao']}>
              <img alt={''} src="https://img.alicdn.com/imgextra/i1/155168396/O1CN01qZhZQI2BtQ7srifM1_!!155168396.png" />
              <span>请使用抖音APP扫一扫预览</span>
            </p>
          </div>
        </div>
      </Container>

      <Box direction="row" justify="center" style={{ position: 'fixed', bottom: 22, left: '50%', transform: 'translateX(calc(-50% + 110px))' }}>
        <Button type="primary" style={{ width: 150 }} onClick={() => history?.push('/activity/list')}>
          暂不投放
        </Button>
      </Box>
    </div>
  );
}

