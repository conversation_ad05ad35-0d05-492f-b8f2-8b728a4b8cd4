import React, { createContext, useReducer, ReactNode, useContext } from 'react';
import { Action, AppState, ModuleType } from './type';
import dayjs from 'dayjs';
import { getExpireDay } from '@/utils';

const expireDay = getExpireDay();
// 初始状态
const initialState: AppState = {
  extra: {
    // 操作类型 add 新增 edit 编辑 view 预览 copy 复制
    operationType: 'add',
    // 原始结束时间 用于复制活动时，判断不可选择原始活动之前的时间
    originalEndTime: dayjs().add(expireDay > 15 ? 15 : expireDay, 'day').format('YYYY-MM-DD 00:00:00'),
    // 活动状态 1未开始 2进行中 3已结束
    activityStatus: 1,
    // 活动地址
    activityUrl: '',
  },
  base: {
    // 活动类型
    activityType: 10202,
    // 模板ID
    templateCode: 1001,
    // 活动名称
    activityName: `会员转段礼${dayjs().format('YYYY-MM-DD')}`,
    // 活动开始时间
    startTime: dayjs().format('YYYY-MM-DD 00:00:00'),
    // 活动结束时间
    endTime: dayjs().add(expireDay > 15 ? 15 : expireDay, 'day').format('YYYY-MM-DD 00:00:00'),
    path: 'pages/index/index',
  },
  threshold: {
    thresholdType: 1,
    thresholdInfo: {},
    isMember: 1,
  },
  excludedSkuList: [],
  order: {
    // 前置订单前推天数
    days: 90,
    // 前置正装sku
    beforeSkuList: [],
    // 前置订单是否延迟发奖 0-不延迟 1-延迟
    isDelayedDisttributionBefore: 1,
    // 前置订单延迟发奖天数
    awardDaysBefore: 7,
  },
  // 中听sku和奖品
  demoSkuList: [],
  repurchase: {
    // 奖品延迟发放 0-不延迟 1-延迟
    isDelayedDisttribution: 1,
    // 延迟天数
    awardDays: 1,
    // 复购奖品列表
    repurchasePrizeList: [],
    // 复购sku列表
    repurchaseSkuList: [],
  },
  rule: '',
  decorate: '',
  share: {
    shareTitle: '会员转段赠好礼！',
    shareContent: '超多惊喜大奖等你来领！',
    mpImg: 'https://img10.360buyimg.com/imgzone/jfs/t1/325989/31/8584/75841/68a68cf5F627a6f6e/09650007194c6cd5.png',
  },
  recommendGoods: [],
  errors: {
    [ModuleType.BASE]: [],
    [ModuleType.THRESHOLD]: [],
    [ModuleType.EXCLUDED_SKU_LIST]: [],
    [ModuleType.ORDER]: [],
    [ModuleType.DEMO_SKU_LIST]: [],
    [ModuleType.REPURCHASE]: [],
    [ModuleType.RULE]: [],
    [ModuleType.SHARE]: [],
    [ModuleType.RECOMMEND_GOODS]: [],
  },
};

// Reducer函数
function reducer(state: AppState, action: Action): AppState {
  switch (action.type) {
    case 'UPDATE_BASE':
      return {
        ...state,
        base: {
          ...state.base,
          ...action.payload,
        },
      };
    case 'UPDATE_THRESHOLD':
      console.log('UPDATE_THRESHOLD', {
        ...state.threshold,
        ...action.payload,
      });
      return {
        ...state,
        threshold: {
          ...state.threshold,
          ...action.payload,
        },
      };
    case 'UPDATE_ORDER':
      console.log('UPDATE_ORDER', {
        ...state.order,
        ...action.payload,
      });
      return {
        ...state,
        order: {
          ...state.order,
          ...action.payload,
        },
      };
    case 'UPDATE_EXCLUDED_SKU_LIST':
      console.log('UPDATE_EXCLUDED_SKU_LIST', action.payload);
      return {
        ...state,
        excludedSkuList: action.payload,
      };
    case 'UPDATE_DEMO_SKU_LIST':
      console.log('UPDATE_DEMO_SKU_LIST', action.payload);
      return {
        ...state,
        demoSkuList: action.payload,
      };
      case 'UPDATE_REPURCHASE':
      console.log('UPDATE_REPURCHASE', action.payload);
      return {
        ...state,
        repurchase: {
          ...state.repurchase,
          ...action.payload,
        },
      };
    case 'UPDATE_RULE':
      return {
        ...state,
        rule: action.payload,
      };
    case 'UPDATE_SHARE':
      return {
        ...state,
        share: {
          ...state.share,
          ...action.payload,
        },
      };
    case 'UPDATE_RECOMMEND_GOODS':
      return {
        ...state,
        recommendGoods: action.payload,
      };
    case 'UPDATE_DECORATE':
      console.log('action.payload', action.payload);
      return {
        ...state,
        decorate: action.payload,
      };
    case 'VALIDATE_MODULE':
      if (!action.module) return state;
      return {
        ...state,
        errors: {
          ...state.errors,
          [action.module]: action.payload,
        },
      };
    case 'CLEAR_ERRORS':
      return {
        ...state,
        errors: {
          ...state.errors,
          [action.module as ModuleType]: [],
        },
      };
    case 'UPDATE_EXTRA':
      console.log('UPDATE_EXTRA', {
        extra: {
          ...state.extra,
          ...action.payload,
        },
      });
      return {
        ...state,
        extra: {
          ...state.extra,
          ...action.payload,
        },
      };
    case 'INIT_MODULE': {
      console.log('INIT_MODULE', {
        ...state,
        ...action.payload.activityData,
        decorate: action.payload.decorationData,
      });
      return {
        ...state,
        ...action.payload.activityData,
        decorate: action.payload.decorationData,
      };
    }
    default:
      return state;
  }
}

// 创建Context
interface ActivityContextType {
  state: AppState;
  dispatch: React.Dispatch<Action>;
}

export const ActivityContext = createContext<ActivityContextType>({
  state: initialState,
  dispatch: () => null,
});

// Context Provider组件
interface ActivityProviderProps {
  children: ReactNode;
}

export function ActivityProvider({ children }: ActivityProviderProps) {
  const [state, dispatch] = useReducer(reducer, initialState);

  return (
    <ActivityContext.Provider value={{ state, dispatch }}>
      {children}
    </ActivityContext.Provider>
  );
}

// 自定义Hook方便使用Context
export function useActivity() {
  const context = useContext(ActivityContext);
  if (!context) {
    // 改成中文
    throw new Error('useActivity必须使用ActivityProvider包裹');
  }
  return context;
}