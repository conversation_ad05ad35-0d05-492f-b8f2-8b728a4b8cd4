import usePagination from '@/hooks/usePagination';
import { downloadExcel, maskSensitiveInfo } from '@/utils';
import { Box, Button, DatePicker2, Input, Message, Pagination, Table } from '@alifd/next';
import { useEffect, useState } from 'react';
import dayjs from 'dayjs';
import { dataAfterWinningLog, dataAfterWinningLogExport } from '@/api/v10202';
import { Auth } from '@/components/Auth';

const pageNum = 1;
const pageSize = 10;

export default function ReceiveData({ activityId }) {
  const defaultSearchParams = {
    activityId,
    pin: '',
    prizeName: '',
    dateRange: [],
  };
  // 搜索相关
  const [searchParams, setSearchParams] = useState({
    ...defaultSearchParams,
    activityId,
  });

  // 表格相关
const [tableData, setTableData] = useState<any>([]);
const [loading, setLoading] = useState(false);

// 通用的参数更新函数
const updateSearchParam = (key: string, value: any) => {
  setSearchParams(prev => ({ ...prev, [key]: value }));
};


const pagination = usePagination(pageNum, pageSize, async (current, size, params) => {
  await fetchData({
    pageNum: current,
    pageSize: size,
    ...params,
  });
});

const fetchData = async (params?: any) => {
  setLoading(true);
  try {
    // const [start, end] = searchParams.timeRange || [];
    // 构建查询参数
    const queryParams = {
      ...searchParams,
      ...params,
      pageNum: pagination.current,
      pageSize: pagination.pageSize,
    };

    const { records, total }: any = await dataAfterWinningLog({
      ...queryParams,
      dateRange: queryParams.dateRange.length > 0 ? [dayjs(queryParams.dateRange[0]).format('YYYY-MM-DD 00:00:00'),dayjs(queryParams.dateRange[1]).format('YYYY-MM-DD 23:59:59')] : [],
    });

    setTableData(records || []);
    pagination.setTotal(total || 0);
  } catch (e) {
    Message.error(e.message);
  } finally {
    setLoading(false);
  }
};

const handleExport = async () => {
  setLoading(true);
  try {
    const data: any = await dataAfterWinningLogExport({
      ...searchParams,
      dateRange: searchParams.dateRange.length > 0 ? [dayjs(searchParams.dateRange[0]).format('YYYY-MM-DD 00:00:00'),dayjs(searchParams.dateRange[1]).format('YYYY-MM-DD 23:59:59')] : [],
      pageNum: pagination.current,
      pageSize: pagination.pageSize,
    });
    downloadExcel(data, `复购奖品领取记录${dayjs().format('YYYY-MM-DD HH:mm')}`);
  } catch (e) {
    Message.error(e.message);
  } finally {
    setLoading(false);
  }
};

const handleSearch = () => {
  pagination.changePage(pageNum);
};

const handleReset = () => {
  setSearchParams(defaultSearchParams);
  pagination.reset(defaultSearchParams);
};
useEffect(() => {
  fetchData().then();
}, []);

  return (
    <Box spacing={16}>
      <Box direction="row" spacing={16} wrap align="center">
        <Input label={'用户PIN'} value={searchParams.pin} onChange={(value) => updateSearchParam('pin', value)} />
        <Input label={'复购奖品名称'} value={searchParams.prizeName} onChange={(value) => updateSearchParam('prizeName', value)} />
        <DatePicker2.RangePicker
          type={'range'}
          label={'领取时间'}
          value={searchParams.dateRange}
          onChange={(value) => updateSearchParam('dateRange', value)}
        />
        <Button type="primary" onClick={handleSearch}>查询</Button>
        <Button onClick={handleReset}>重置</Button>
        <Auth authKey={'activity_list_data_export'} >
          <Button
            type="secondary"
            onClick={handleExport}
          >
            导出
          </Button>
        </Auth>
      </Box>
      <Table
        dataSource={tableData}
        loading={loading}
      >
        <Table.Column
          title="用户pin"
          dataIndex="encryptPin"
          cell={(value) => {
          return <div >{maskSensitiveInfo(value, 1, 1, 6)}</div>;
        }}
        />
        <Table.Column title="复购奖品领取时间" dataIndex="createTime" cell={(value) => (value ? dayjs(value).format('YYYY-MM-DD HH:mm:ss') : '-')} />
        <Table.Column title="奖品名称" dataIndex="prizeName" />
        <Table.Column title="发放状态" dataIndex="status" />
        <Table.Column title="复购订单号" dataIndex="orderId" />
        <Table.Column title="复购订单sku" dataIndex="skuIds" />
        <Table.Column title="复购订单下单时间" dataIndex="orderStartTime" cell={(value) => (value ? dayjs(value).format('YYYY-MM-DD HH:mm:ss') : '-')} />

      </Table>
      <Box direction="row" justify="flex-end" margin={[16, 0, 0, 0]}>
        <Pagination
          {...pagination.paginationProps}
          shape={'arrow-only'}
          totalRender={(total) => `共 ${total} 条`}
          pageSizeSelector="dropdown"
          pageSizePosition="end"
        />
      </Box>
    </Box>
  );
}
