import usePagination from '@/hooks/usePagination';
import { downloadExcel, maskSensitiveInfo } from '@/utils';
import { Balloon, Box, Button, DatePicker2, Icon, Input, Message, Pagination, Select, Table } from '@alifd/next';
import { useEffect, useState } from 'react';
import dayjs from 'dayjs';
import { dataDemoWinningLog, dataDemoWinningLogExport, dataGetSectionInfo } from '@/api/v10202';
import { Auth } from '@/components/Auth';

const pageNum = 1;
const pageSize = 10;

export default function ReceiveData({ activityId }) {
  const [goodsLineList, setGoodsLineList] = useState<Array<{ label: string; value: string | number }>>([]);
  const [sectionNumList, setSectionNumList] = useState<Array<{ label: string; value: string | number }>>([]);

  const defaultSearchParams = {
    activityId,
    pin: '',
    prizeName: '',
    goodsLine: '',
    dateRange: [],
    sectionNum: '',
  };
  // 搜索相关
  const [searchParams, setSearchParams] = useState({
    ...defaultSearchParams,
    activityId,
  });

  // 表格相关
  const [tableData, setTableData] = useState<any>([]);
  const [loading, setLoading] = useState(false);

  // 通用的参数更新函数
  const updateSearchParam = (key: string, value: any) => {
    setSearchParams(prev => ({ ...prev, [key]: value }));
  };


  const pagination = usePagination(pageNum, pageSize, async (current, size, params) => {
    await fetchData({
      pageNum: current,
      pageSize: size,
      ...params,
    });
  });

  const fetchData = async (params?: any) => {
    // console.log('fetchData====', params, searchParams);
    setLoading(true);
    try {
      // const [start, end] = searchParams.timeRange || [];
      // 构建查询参数
      const queryParams = {
        ...searchParams,
        pageNum: pagination.current,
        pageSize: pagination.pageSize,
        ...params,
      };
      const { records, total }: any = await dataDemoWinningLog({
        ...queryParams,
        dateRange: queryParams.dateRange.length > 0 ? [dayjs(queryParams.dateRange[0]).format('YYYY-MM-DD 00:00:00'),dayjs(queryParams.dateRange[1]).format('YYYY-MM-DD 23:59:59')] : [],
      });
      setTableData(records || []);
      pagination.setTotal(total || 0);

    } catch (e) {
      Message.error(e.message);
    } finally {
      setLoading(false);
    }
  };

  const handleExport = async () => {
    setLoading(true);
    try {
      const data: any = await dataDemoWinningLogExport({
        ...searchParams,
        dateRange: searchParams.dateRange.length > 0 ? [dayjs(searchParams.dateRange[0]).format('YYYY-MM-DD 00:00:00'),dayjs(searchParams.dateRange[1]).format('YYYY-MM-DD 23:59:59')] : [],
        pageNum: pagination.current,
        pageSize: pagination.pageSize,
      });
      downloadExcel(data, `小罐奖品领取记录${dayjs().format('YYYY-MM-DD HH:mm')}`);
    } catch (e) {
      Message.error(e.message);
    } finally {
      setLoading(false);
    }
  };

  const handleSearch = () => {
    pagination.changePage(pageNum);
  };

  const handleReset = () => {
    setSearchParams(defaultSearchParams);
    pagination.reset(defaultSearchParams);
  };
  // 获取品线和段位
  const getSelectionMapList = () => {
    dataGetSectionInfo({
      activityId: activityId,
    })
      .then((res) => {
        // 将goodsLine对象转换为Select组件需要的数组格式
        const goodsLineOptions = Object.entries(res.goodsLine || {}).map(([key, value]) => ({
          label: value,
          value: key,
        }));
        // 将sectionInfo对象转换为Select组件需要的数组格式
        const sectionOptions = Object.entries(res.sectionInfo || {}).map(([key, value]) => ({
          label: key,
          value,
        }));
        setGoodsLineList(goodsLineOptions);
        setSectionNumList(sectionOptions);
      })
      .catch((e) => {
        setLoading(false);
      });
  };
  useEffect(() => {
    getSelectionMapList();
    fetchData().then();
  }, []);

  return (
    <Box spacing={16}>
      <Box direction="row" spacing={16} wrap align="center">
        <Input label={'用户PIN'} value={searchParams.pin} onChange={(value) => updateSearchParam('pin', value)} />
        <Input label={'奖品名称'} value={searchParams.prizeName} onChange={(value) => updateSearchParam('prizeName', value)} />
        <Select
          label="订单品线"
          value={searchParams.goodsLine}
          onChange={val => updateSearchParam('goodsLine', val)}
          style={{ width: 200 }}
        >
          <Select.Option value="">全部</Select.Option>
          {goodsLineList.map((item, index) => (
            <Select.Option
              key={index}
              value={item.value}
            >
              {item.label}
            </Select.Option>
          ))}
        </Select>

        <Select
          label="订单段位"
          value={searchParams.sectionNum ? searchParams.sectionNum : ''}
          onChange={val => updateSearchParam('sectionNum', val)}
          style={{ width: 200 }}
        >
          <Select.Option value="">全部</Select.Option>
          {sectionNumList.map((item, index) => (
            <Select.Option
              key={index}
              value={item.value}
            >
              {item.label}
            </Select.Option>
          ))}
        </Select>
        <DatePicker2.RangePicker
          type={'range'}
          label={'领取时间'}
          value={searchParams.dateRange}
          onChange={(value) => updateSearchParam('dateRange', value)}
        />
        <Button type="primary" onClick={handleSearch}>查询</Button>
        <Button onClick={handleReset}>重置</Button>
        <Auth authKey={'activity_list_data_export'} >
          <Button
            type="secondary"
            onClick={handleExport}
          >
            导出
          </Button>
        </Auth>
      </Box>
      <Table
        dataSource={tableData}
        loading={loading}
      >
        <Table.Column
          title="用户昵称"
          dataIndex="nickName"
          cell={(value) => {
            return <div >{maskSensitiveInfo(value, 1, 1)}</div>;
          }}
        />
        <Table.Column
          title="用户pin"
          dataIndex="encryptPin"
          cell={(value) => {
            return <div >{maskSensitiveInfo(value, 1, 1, 6)}</div>;
          }}
        />
        <Table.Column title="领取时间" dataIndex="createTime" cell={(value) => (value ? dayjs(value).format('YYYY-MM-DD HH:mm:ss') : '-')} />
        <Table.Column title="奖品名称" dataIndex="prizeName" />
        <Table.Column title="发放状态" dataIndex="status" />
        <Table.Column title="资产类型" dataIndex="prizeType" />
        <Table.Column title="前置订单号" dataIndex="orderId" />
        <Table.Column
          width={100}
          title={
            <div>
              订单段位
              <Balloon.Tooltip trigger={<Icon style={{marginLeft: '4px'}} type="prompt" size="xs" />}>
                参加活动前置订单段位
              </Balloon.Tooltip>
            </div>
          }
          dataIndex="sectionNum"
        />
        {/* <Table.Column title="订单段位" dataIndex="sectionNum" /> */}
        <Table.Column title="订单品线" dataIndex="goodsLine" />
        <Table.Column title="前置订单完成时间" dataIndex="orderEndTime" cell={(value) => (value ? dayjs(value).format('YYYY-MM-DD HH:mm:ss') : '-')} />
        {/* <Table.Column title="是否完成复购" dataIndex="hasRepurchased" cell= {(item,index,row) => {
          return <div>{row.hasRepurchased === '0' ? '否' : '是'}</div>
        }} /> */}
        <Table.Column title="是否完成复购" dataIndex="hasRepurchased" />
      </Table>
      <Box direction="row" justify="flex-end" margin={[16, 0, 0, 0]}>
        <Pagination
          {...pagination.paginationProps}
          shape={'arrow-only'}
          totalRender={(total) => `共 ${total} 条`}
          pageSizeSelector="dropdown"
          pageSizePosition="end"
        />
      </Box>
    </Box>
  );
}
