import { maskSensitiveInfo } from '@/utils';
import { Box, Button, Input, Message, Table } from '@alifd/next';
import { useEffect, useState } from 'react';
import dayjs from 'dayjs';
import { dataTotalWinningLog } from '@/api/v10202';

export default function ReceiveData({ activityId }) {
  const defaultSearchParams = {
    pin: '',
  };
  // 搜索相关
  const [searchParams, setSearchParams] = useState({
    ...defaultSearchParams,
  });

  // 表格相关
const [tableData, setTableData] = useState<any>([]);
const [loading, setLoading] = useState(false);

// 通用的参数更新函数
const updateSearchParam = (key: string, value: any) => {
  setSearchParams(prev => ({ ...prev, [key]: value }));
};
const fetchData = async (params?: any) => {
  setLoading(true);
  try {
    // const [start, end] = searchParams.timeRange || [];
    // 构建查询参数
    const queryParams = {
      ...searchParams,
    };

    const res: any = await dataTotalWinningLog({
      ...queryParams,
      ...params,
    });
    // console.log(res, 'data=========');
    setTableData(res);
  } catch (e) {
    Message.error(e.message);
  } finally {
    setLoading(false);
  }
};

const handleSearch = () => {
  fetchData().then();
};

const handleReset = () => {
  setSearchParams(defaultSearchParams);
  fetchData({
    pin: '',
  }).then();
};
useEffect(() => {
  fetchData().then();
}, []);

  return (
    <Box spacing={16}>
      <Box direction="row" spacing={16} wrap align="center">
        <Input label={'用户PIN'} value={searchParams.pin} onChange={(value) => updateSearchParam('pin', value)} />
        <Button type="primary" onClick={handleSearch}>查询</Button>
        <Button onClick={handleReset}>重置</Button>
      </Box>
      <Table
        dataSource={tableData}
        loading={loading}
      >
        <Table.Column
          title="用户pin"
          dataIndex="encryptPin"
          cell={(value) => {
          return <div >{maskSensitiveInfo(value, 1, 1, 6)}</div>;
        }}
        />
        <Table.Column title="领奖活动id" dataIndex="activityId" />
        <Table.Column title="领奖店铺id" dataIndex="shopId" />
        <Table.Column title="领奖时间" dataIndex="createTime" cell={(value) => (value ? dayjs(value).format('YYYY-MM-DD HH:mm:ss') : '-')} />
        <Table.Column title="奖品名称" dataIndex="prizeName" />
        <Table.Column title="活动奖品类型" dataIndex="activityPrizeType" />
        <Table.Column title="活动奖品段数" dataIndex="sectionNum" />
        <Table.Column title="发放状态" dataIndex="status" />
      </Table>
    </Box>
  );
}
