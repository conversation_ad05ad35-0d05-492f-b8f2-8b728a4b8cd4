import { Box, Tab } from '@alifd/next';
import SmallPrizeReceiveRecord from './smallPrizeReceiveRecord';
import RepurchasePrizeReceivedRecord from './repurchasePrizeReceivedRecord';
import AllPrizeRecord from './allPrizeRecord';
import { useLocation } from 'ice';
import { useState } from 'react';
import Container from '@/components/Container';

export default function Activity10202Data() {
  const location = useLocation();
  const { record } = location.state || {};

  const { activityId, startTime, endTime } = record || {};
  const [activeTab, setActiveTab] = useState('receiveSmall');

  const props = {
    activityId: activityId,
    startTime,
    endTime,
  };
const tabList = [
  { title: '小罐奖品领取记录', key: 'receiveSmall', component: SmallPrizeReceiveRecord },
  { title: '复购奖品领取记录', key: 'receiveRepurchase', component: RepurchasePrizeReceivedRecord },
  { title: '总获奖记录查询', key: 'allPrize', component: AllPrizeRecord },
  // { title: '活动数据', key: 'data', component: DataReport },
];
  return (
    <div>
      <Tab
        shape="pure"
        activeKey={activeTab}
        onChange={setActiveTab}
      >
        {tabList.map(tab => {
          const Component = tab.component;
          return (
            <Tab.Item
              key={tab.key}
              title={tab.title}
            >
              <Box padding={[20, 0]}>
                <Container>
                  {activeTab === tab.key && <Component {...props} />}
                </Container>
              </Box>
            </Tab.Item>
          );
        })}
        Record
      </Tab>
      {/* <Tab activeKey={activeTab} onChange={setActiveTab}>
        <Tab.Item title="小罐奖品领取记录" key="receiveSmall">
          <SmallPrizeReceiveRecord x-if={activeTab === 'receiveSmall'} {...props} />
        </Tab.Item> */}
      {/* <Tab.Item title="实物中奖信息" key="award">
          <AwardData x-if={activeTab === 'award'} {...props} />
        </Tab.Item>
        <Tab.Item title="抽奖机会获取明细" key="draw">
          <DrawData x-if={activeTab === 'draw'} {...props} />
        </Tab.Item>
        <Tab.Item title="活动数据" key="activity">
          <ActivityData x-if={activeTab === 'activity'} {...props} />
        </Tab.Item>
        <Tab.Item title="奖品数据" key="prize">
          <PrizeData x-if={activeTab === 'prize'} {...props} />
        </Tab.Item> */}
      {/* </Tab> */}
    </div>
  );
}