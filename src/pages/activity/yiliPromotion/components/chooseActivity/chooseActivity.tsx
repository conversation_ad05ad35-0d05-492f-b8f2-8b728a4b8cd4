import { Dialog } from '@alifd/next';
import ChooseActivityModalContent from './ChooseActivityModalContent';
import { addDialogRef } from '@/utils/dialogMapper';

export default function chooseActivity() {
  return new Promise(resolve => {
    const dialogRef = Dialog.show({
      v2: true,
      title: '选择活动',
      width: 900,
      centered: true,
      closeMode: ['close'],
      content: (
        <ChooseActivityModalContent
          onResolve={val => {
            resolve(val);
            dialogRef.hide();
          }}
        />
      ),
      footer: false,
    });
    addDialogRef(dialogRef);
  });
}
