import { useEffect, useState } from 'react';
import { Box, Button, Table, Message } from '@alifd/next';
import chooseActivity from './components/chooseActivity/chooseActivity';
import Container from '@/components/Container';
import { activityCreateActivityEffective, activityGetActivityEffective } from '@/api/yili';
import TimeDiff from '@/components/TimeDiff/TimeDiff';
import constant from '@/utils/constant';
import { activityGetAllActivityTypes } from '@/api/common';

export default function LiveCardIndexPage() {
  const [activityTypeList, setActivityTypeList] = useState<any[]>([]);
  const [activityPlanList, setActivityPlanList] = useState([]);
  const [loading, setLoading] = useState<boolean>(false);

  // 组件加载时根据路由的tab参数设置cardSize
  useEffect(() => {
    getActivityTypeList();
    loadData();
  }, []);

  const getActivityTypeList = async () => {
    const res: any = await activityGetAllActivityTypes();
    setActivityTypeList(res);
  };

  const getActivityTypeLabel = activityType => {
    for (const key in activityTypeList) {
      if (activityTypeList[key].value === activityType) {
        return activityTypeList[key].label;
      }
    }
    return '--';
  };

  // 整合后的数据加载函数
  const loadData = async (obj?: any) => {
    setLoading(true);
    console.log('current', JSON.parse(localStorage.getItem(constant.LZ_CURRENT_SHOP) || '{}'));
    try {
      // 加载投放活动
      const params: any = {
        shopId: JSON.parse(localStorage.getItem(constant.LZ_CURRENT_SHOP) || '{}').shopId,
      };
      const res: any = await activityGetActivityEffective(params);
      setActivityPlanList(res || []);
    } catch (err) {
      console.error(err);
      Message.error(err.message);
    } finally {
      setLoading(false);
    }
  };

  const addPlan = async () => {
      try {
        const data: any = await chooseActivity();
        setLoading(true);
        const params: any = {
          activityId: data.id,
        };
        await activityCreateActivityEffective(params);
        // Message.success('新建投放计划成功');
        await loadData();
      } catch (e) {
        Message.error(e.message);
      } finally {
        setLoading(false);
      }
  };

  return (
    <Container>
      <Box
        direction="row"
        align="center"
        justify="space-between"
      >
        <Box
          direction="row"
          spacing={16}
          wrap
        >
          <Button
            type="primary"
            onClick={() => addPlan()}
          >
            选择投放活动
          </Button>
        </Box>
      </Box>

      {/* 表格 */}
      <Table
        dataSource={activityPlanList}
        loading={loading}
        hasBorder
        style={{ marginTop: '10px' }}
      >
        <Table.Column
          title="活动名称"
          dataIndex="activityName"
        />
        <Table.Column
          title="活动类型"
          dataIndex="activityType"
          cell={value => getActivityTypeLabel(value)}
        />
        <Table.Column
          title="活动时间"
          cell={(_, __, record) => (
            <TimeDiff
              startTime={record.startTime}
              endTime={record.endTime}
            />
          )}
        />
      </Table>
    </Container>
  );
}
