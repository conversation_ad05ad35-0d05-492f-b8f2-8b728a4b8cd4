import Container from '@/components/Container';
import { useStep } from './hook';
import { Button, Form, Message, Radio } from '@alifd/next';
import NumberInput from '@/components/NumberInput/NumberInput';
import { useActivity } from '@/pages/activity/10201/create/reducer';
import { ACTIVITY_STATUS } from '@/utils/constant';
import ExcelImport from '../components/ExcelImport';
import { downloadExcel } from '@/utils';
import { seriesTemplateExport } from '@/api/v10201';
import { usePrizeAndSku } from '@/pages/activity/10201/create/components/setting/prizeAndSku/hooks';
import { SeriesList } from '@/pages/activity/10201/create/type';
import { getProductList } from '@/api/sku';

export default function Step() {
  const { step, updateStep } = useStep();
  const { prizeAndSku, updatePrizeAndSku } = usePrizeAndSku();
  const { state } = useActivity();
  const { operationType, activityStatus } = state.extra;

  const isEdit = operationType === 'edit';
  const isView = operationType === 'view';
  const unStart = activityStatus === ACTIVITY_STATUS.NOT_STARTED;
  const isCopy = operationType === 'copy';
  const isCreate = operationType === 'add';

  const needDisable = (isEdit || isView) && unStart;
  console.log('ACTIVITY_STATUS.NOT_STARTED', ACTIVITY_STATUS.NOT_STARTED, activityStatus);

  const downloadTemplate = async () => {
    try {
      const data: any = await seriesTemplateExport();
      downloadExcel(data, '商品导入模板');
    } catch (error) {
      Message.error(error.message);
    }
  };

  const setTemporarySeriesList = async (data) => {
    const getSkuInfo = async (list, index) => {
      const spuRes = await getProductList({
        productIds: list
            .map((item) => item.spuId)
            .filter((e) => e)
            .slice(0, 10) // 使用 slice 而不是 splice
            .join(','), // 转换为逗号分割的字符串
        pageNum: 1, // 必需参数
        pageSize: 10, // 必需参数
      });
      const successSpuListArr = spuRes.data || [];

      // 给预览的spu 添加 productLine
      successSpuListArr.forEach((previewSpu: any) => {
        const matchingSku = data[0].seriesSkuInfoList.find((spu) => spu.spuId === previewSpu.numIid);
        if (matchingSku && matchingSku.productLine) {
          // 直接修改原对象，而不是重新赋值局部变量
          previewSpu.productLine = matchingSku.productLine;
        }
      });

      data[index].previewSpuList = successSpuListArr;
    };

    await Promise.all(data.map((item, index) => getSkuInfo(item.seriesSkuInfoList, index)));
    // 以前的数据结构就是只有一个系列，不知道为啥这么设计，后端不想改数据结构，搬过来了。。
    updatePrizeAndSku({ seriesSkuList: data[0].seriesSkuInfoList });

    const seriesList: SeriesList[] = [];
    data.forEach((item) => {
      seriesList.push({
        seriesName: item.seriesName,
        seriesSkuList: item.seriesSkuInfoList,
        previewSpuList: item.previewSpuList,
        perReceiveCount: 1,
        totalReceiveCount: 1,
        prizeReceiveLimit: 1,
        stepList: [],
      });
    });
    updateStep({ fileList: data });
    updatePrizeAndSku({ seriesList });
  };

  return (
    <Container title="阶梯设置" style={{ marginBottom: 10 }}>
      {
            (isCreate || isCopy || unStart) && (
            <>
              <Form.Item label=" " >
                <Message type="notice" style={{ marginBottom: 10 }}>
                  导入须知： <br />
                  1.导入前请下载模板，将你要上传的数据放入模板中，使用模板进行导入。
                  <br />
                  2.单次导入最大5M，导入中请不要关闭此页面。
                  <br />
                  <Button text type="primary" onClick={downloadTemplate}>
                    下载模板
                  </Button>
                </Message>
              </Form.Item>
              <Form.Item label=" ">
                <ExcelImport
                  buttonText="上传订单商品系列数据"
                  action={`${process.env.ICE_BASE_URL || ''}/10201/importSeriesExcel`}
                  onSuccess={(res) => {
                          const { response } = res;

                          // code 是 200 就保留文件名称，其他情况不展示文件名
                          if (response && response.code === 200) {
                            if (response.data) {
                              setTemporarySeriesList(response.data);
                              Message.success('导入成功');
                              return true; // 返回 true 保留文件名
                            } else {
                              Message.error('导入数据为空，请检查文件内容');
                              return false; // 返回 false 不展示文件名
                            }
                          } else {
                            // code 不是 200 (比如 500)，不展示文件名
                            const errorMessage = response?.message || '导入失败，请检查数据格式';
                            Message.error(errorMessage);
                            return false; // 返回 false 不展示文件名
                          }
                        }}
                  onError={(error) => {
                          console.error('上传失败：', error);
                          const errorMessage = error?.response?.message || error?.message || '网络错误，请重试';
                          Message.error(errorMessage);
                        }}
                />
              </Form.Item>
            </>
            )
        }
      <Form.Item
        label="限制活动内总兑换次数"
        required
      >
        <Radio.Group
          value={step.actLimitActTotalReceiveCount}
          onChange={(value: number) => {
                const updateData: any = { actLimitActTotalReceiveCount: value };
                // 当切换到限制时，如果当前总兑换次数为0，则设置默认值为1
                if (value === 1 && step.actTotalReceiveCount === 0) {
                  updateData.actTotalReceiveCount = 1;
                }
                updateStep(updateData);
              }}
        >
          <Radio value={1}>限制</Radio>
          <Radio value={0}>不限制</Radio>
        </Radio.Group>
      </Form.Item>
      {!!step.actLimitActTotalReceiveCount && (
        <Form.Item required requiredMessage={'请输入活动内总兑换次数'} label="最多兑换">
          <NumberInput
            min={needDisable ? step.actTotalReceiveCount : 1}
            max={9999999}
            step={1}
            type="inline"
            value={step.actTotalReceiveCount}
            onChange={(actTotalReceiveCount: number) => {
                    updateStep({ actTotalReceiveCount });
                  }}
            name={'actTotalReceiveCount'}
          />{' '}
          次
        </Form.Item>
        )}
    </Container>
  );
}