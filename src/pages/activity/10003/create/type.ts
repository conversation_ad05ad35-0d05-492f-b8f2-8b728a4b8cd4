export enum ModuleType {
  BASE = 'base',
  THRESHOLD = 'threshold',
  CHANCE = 'chance',
  RECEIVE = 'receive',
  PRIZE = 'prize',
  RULE = 'rule',
  SHARE = 'share',
  RECOMMEND_GOODS = 'recommendGoods',
}
// Action类型定义
type ActionType =
  | 'UPDATE_BASE'
  | 'UPDATE_THRESHOLD'
  | 'UPDATE_CHANCE'
  | 'UPDATE_RECEIVE'
  | 'UPDATE_PRIZE'
  | 'UPDATE_RULE'
  | 'UPDATE_SHARE'
  | 'UPDATE_RECOMMEND_GOODS'
  | 'UPDATE_DECORATE'
  | 'VALIDATE_MODULE'
  | 'UPDATE_EXTRA'
  | 'CLEAR_ERRORS'
  | 'INIT_MODULE';

export interface Action {
  type: ActionType;
  payload?: any;
  module?: ModuleType;
}

// 基础信息模块状态定义
export interface BaseInfoState {
  activityType: number;
  activityName: string;
  startTime: string;
  endTime: string;
  templateCode: number;
  activityId: string;
  path: string;
}
export interface ExtraState {
  operationType: 'edit' | 'add' | 'copy' | 'view';
  originalEndTime: string;
  // 活动状态 1未开始 2进行中 3已结束
  activityStatus: number;
  activityUrl: string;
  mpImg: string;
}

export interface ThresholdState {
  thresholdType: number;
  thresholdInfo: any;
}

export interface ChanceState {
  // 免费赠送次数
  freeChance: number;
  taskList: any[];
}

export interface PrizeState {
  prizeStartTime: string;
  prizeEndTime: string;
  prizeList: any[];
}

export interface SkuItem {
  numIid: string;
  name: string;
  picUrl: string;
  sellNum: number;
}

export interface ShareState {
  shareTitle: string;
  shareContent: string;
  mpImg: string;
}

export interface ReceiveState {
  // 1 手动领奖 2 自动领奖
  receiveType: number;
}

export interface AppState {
  extra: ExtraState;
  base: BaseInfoState;
  threshold: ThresholdState;
  chance: ChanceState;
  receive: ReceiveState;
  prize: PrizeState;
  rule: string;
  decorate: string;
  share: ShareState;
  recommendGoods: SkuItem[];
  errors: Record<ModuleType, string[]>;
}
