import Container from '@/components/Container';
import { Form, Button, Box, Input, Field } from '@alifd/next';
import { useRule } from './hook';
import { useActivity } from '../../reducer';
import dayjs from 'dayjs';
import { PrizeTypeEnum } from '@/utils';

/**
 * 规则设置页面
 * 包含门槛设置、订单规则、奖品设置等
 */
export default function Rule({ goNextStep, goPrevStep }) {
  const { rule, updateRule } = useRule();
  // 获取全局reducer状态
  const { state } = useActivity();
  // 收集自动生成规则所需要的模块信息
  const { base, threshold, chance, prize } = state;

  // 操作类型
  const { operationType } = state.extra;
  // 是否禁用
  const needDisable = operationType === 'view';
  // form表单
  const field = Field.useField();

  // 验证及下一步
  const handleSubmit = () => {
    // 这里可以添加验证逻辑
    field.validatePromise().then(({ errors }) => {
      if (errors) return;
      goNextStep();
    });
  };
  // 上一步
  const handleBack = () => {
    goPrevStep();
  };
  /**
   * 获取任务说明
   * @returns string
   */
  const getTaskText = () => {
    return chance.taskList.map((item, index) => {
      // 获取任务内容的JSX
      // const taskContent = renderTaskContent(item);
      // // 提取文本内容
      // const taskContentText = extractTextFromJSX(taskContent);
      // 渲染内容
      return `（${index + 1}）${item.taskTitle}：${item.taskDesc}`;
    }).join('\n');
  };
  /**
   * 获取奖品说明
   * @returns string
   */
  const getPrizeText = () => {
    return prize.prizeList.filter(item => item.lotteryType !== PrizeTypeEnum.THANKS.value).map((item, index) => {
      return `（${index + 1}）${item.lotteryName}：数量：${item.prizeNum || 1}份，价值：${item.price || 0}${item.lotteryType === PrizeTypeEnum.MEMBER_POINT.value ? '积分' : '元'}/份`;
    }).join('\n');
  };
  // 自动生成规则
  const generateRule = () => {
    const autoText = `1、活动时间：
${dayjs(base.startTime).format('YYYY-MM-DD HH:mm:ss')}至${dayjs(base.endTime).format('YYYY-MM-DD HH:mm:ss')}；
2、活动对象：
${threshold.thresholdType === 1 ? '全体用户' : `${threshold.thresholdInfo.crowdName}`}
3、任务说明：
${getTaskText()}
4、奖品说明
${getPrizeText()}
5、若用户存在刷奖等恶意行为，一经发现将取消抽奖资格（如奖品已经发放，有权追回奖品）；
奖品发放形式：
实物奖品：
（1）实物奖采取【单独寄送】方式发放，获奖用户需在开奖后填写姓名、联系电话、详细地址信息。如因用户原因无法联系上，即奖品作废不再补发，或如用户未填写真实有效的信息或填写收货信息不详，均视为放弃奖品；
（2）中奖客户信息收集：页面弹屏提示中奖客户提供收货地址、号码和奖项；(活动结束未填写对应收货信息，视为放弃)；
虚拟奖品：
（1）优惠券：领奖后奖品自动发放至抖音账户；
（2）积分：领奖后奖品自动发放至抖音账户；
【活动参与主体资格】
（1）每位自然人用户仅能使用一个抖音账号参与活动，手机号码等任意信息一致或指向同一用户，视为同一用户，则第一个参与本活动的账号参与结果有效，其他账号参与本活动均视为无效；若发现同一位用户使用不同账号重复参与活动，承办方有权取消其获奖资格；
【注意事项】
（1）活动过程中，凡以不正当手段（如作弊领取、恶意套取、刷信誉、虚假交易、扰乱系统、实施网络攻击等）参与本次活动的用户，商家有权终止其参与活动，并取消其参与资格（如优惠已发放，商家有权追回），如给商家造成损失的，商家将保留向违规用户继续追索的权利；
（2）如遇不可抗力(包括但不限于重大灾害事件、活动受政府机关指令需要停止举办或调整的、活动中存在大面积作弊行为、活动遭受严重网络攻击或因系统故障导致活动中奖名单大批量出错，活动不能正常进行的)，商家有权取消、修改或暂停本活动。
（3）是否获得优惠以活动发布者后台统计结果为准；
    `;
    updateRule(autoText);
    field.setError('rule', '');
  };

  return (
    <Form className="activity-rule-form" field={field}>
      <Container title={'活动规则'}>
        <Box direction="row" justify="flex-end" align="center" spacing={10} margin={[0, 0, 10, 0]}>
          <span style={{ color: 'var(--color-text-secondary)' }}>
            提交活动前请核对活动规则，可根据实际情况，对内容进行适当编辑
          </span>
          <Button type="secondary" onClick={generateRule} disabled={needDisable}>
            生成规则
          </Button>
        </Box>

        <Form.Item name="rule" label="" required requiredMessage={'请生成活动规则'}>
          <Input.TextArea
            maxLength={2000}
            disabled={needDisable}
            rows={24}
            cutString
            showLimitHint
            composition
            value={rule}
            onChange={val => updateRule(val)}
            placeholder="请输入活动规则"
          />
        </Form.Item>
      </Container>

      <Box direction="row" justify="center" spacing={16} margin={[-30, 0, 0, 0]}>
        <Button onClick={handleBack}>上一步</Button>
        <Button type="primary" style={{ width: 150 }} onClick={handleSubmit}>
          下一步：氛围定制
        </Button>
      </Box>
    </Form>
  );
}
