import Container from '@/components/Container';
import { QRCodeSVG } from 'qrcode.react';
import styles from './index.module.scss';
import { Box, Button } from '@alifd/next';
import { history } from '@ice/runtime';
import { useActivity } from '../../reducer';
import constant from '@/utils/constant';

const defaultQRCodeUrl = {
  45693550: 'https://m.zjbyte.net/share/douyin/?token=RjQ1M0FBRDZGMDZCNzZleW96bzh4&share_channel=scan',
  20421521: 'https://m.zjbyte.net/share/douyin/?token=QTYwMURCQTRBQzcwNzZleW96cDBw&share_channel=scan',
  7778309: 'https://m.zjbyte.net/share/douyin/?token=QjU3MzE2RjE5ODRFNzZleW96c2V4&share_channel=scan',
};

const promotionList = [
  {
    title: '首页feed流',
    image: 'https://novae.oss-cn-zhangjiakou.aliyuncs.com/douyin/images/feed.png',
    fn: () => {
      history?.push('/activity/promotion', { tab: 3 });
    },
  },
  {
    title: '直播间小雪花',
    image: 'https://novae.oss-cn-zhangjiakou.aliyuncs.com/douyin/images/snow.png',
    fn: () => {
      history?.push('/activity/promotion', { tab: 2 });
    },
  },
  {
    title: '店铺首页banner',
    image: 'https://novae.oss-cn-zhangjiakou.aliyuncs.com/douyin/images/home.png',
    fn: () => {
      window.open('https://fxg.jinritemai.com/login', '_blank');
    },
  },
];

export default function Complete() {
  const { state } = useActivity();
  const { activityUrl, operationType } = state.extra;

  const isCreate = operationType === 'add';

  const appInfo = JSON.parse(localStorage.getItem(constant.LZ_CURRENT_SHOP) || '{}');

  return (
    <div className={styles.complete}>
      <Container>
        <div className={styles['qr-img-container']}>
          <QRCodeSVG
            value={activityUrl || defaultQRCodeUrl[appInfo.shopId]}
            className="qr-img"
          />
          <div className={styles['qr-img-des']}>
            <p className={styles['qr-img-message']}>{`活动${isCreate ? '创建' : '更新'}成功`}</p>
            <p className={[styles['qr-img-tips'], 'text-primary'].join(' ')}>
              重要提示：您的活动属于无线互动类，需选择推广渠道并投放，活动才能透出哦~
            </p>
            <p className={styles['qr-img-use-taobao']}>
              <img alt={''} src="https://img.alicdn.com/imgextra/i1/155168396/O1CN01qZhZQI2BtQ7srifM1_!!155168396.png" />
              <span>请使用抖音APP扫一扫预览</span>
            </p>
          </div>
        </div>
      </Container>
      <Container style={{ marginBottom: 0 }}>
        <Box direction="row" spacing={32}>
          {promotionList.map((item) => (
            <div key={item.title} className={styles['promotion-item']}>
              <img src={item.image} />
              <p>{item.title}</p>
              <Button type="primary" onClick={item.fn}>去投放</Button>
              <Button type="primary" text style={{ marginTop: 10 }} onClick={() => window.open('https://gyj4qdmjsv.feishu.cn/wiki/LgaDwy5rGiKgrskaATccyBUOnLg?from=from_copylink', '_blank')}>查看投放教程</Button>
            </div>
          ))}
        </Box>
      </Container>

      <Box
        direction="row"
        justify="center"
        style={{
          position: 'fixed',
          bottom: 22,
          left: '50%',
          transform: 'translateX(calc(-50% + 110px))',
        }}
      >
        <Button
          type="primary"
          style={{ width: 150 }}
          onClick={() => {
          history?.push('/activity/list');
        }}
        >暂不投放</Button>
      </Box>

    </div>

  );
}
