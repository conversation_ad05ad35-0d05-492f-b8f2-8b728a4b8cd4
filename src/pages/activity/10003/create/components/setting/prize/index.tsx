import Container from '@/components/Container';
import { usePrize } from './hooks';
import { Box, DatePicker2, Form } from '@alifd/next';
import PrizeSendTable from '@/components/PrizeSendTable';
import { useActivity } from '../../../reducer';
import { useBaseInfo } from '../base/hooks';
import { ACTIVITY_STATUS } from '@/utils/constant';
import { useEffect } from 'react';
import dayjs from 'dayjs';
import { useReceive } from '../receive/hooks';

export default function Prize() {
  const { prize, updatePrize, errors } = usePrize();
  const { baseInfo } = useBaseInfo();
  const { receive } = useReceive();
  const { state } = useActivity();
  const { activityStatus, operationType } = state.extra;

  const { receiveType } = receive;

  const isView = operationType === 'view';
  const isEdit = operationType === 'edit';
  const unStart = activityStatus !== ACTIVITY_STATUS.NOT_STARTED;
  const hasPrizeError = errors.some(err => err.includes('奖品'));
  const hasTimeError = errors.some(err => err.includes('时间'));


  useEffect(() => {
    // 当活动结束时间发生改变时，重新设置公示时间为活动结束时间前2天
    if (baseInfo.endTime) {
      updatePrize({
        prizeEndTime: dayjs(baseInfo.endTime).format('YYYY-MM-DD HH:mm:ss'),
      }, true);
    }
  }, [baseInfo]);

  useEffect(() => {
    updatePrize({
      prizeEndTime: dayjs(baseInfo.endTime).format('YYYY-MM-DD HH:mm:ss'),
    }, true);
  }, [receiveType]);

  return (
    <Container
      title="奖品设置"
      subtitle="用户单次抽中奖品概率：取决于用户手中的抽签码数量"
    >
      <Form.Item
        label="开奖公示时间"
        required
        validateState={hasTimeError ? 'error' : undefined}
        help={hasTimeError ? errors.find(err => err.includes('时间')) : undefined}
      >
        <DatePicker2.RangePicker
          value={[prize.prizeStartTime, prize.prizeEndTime]}
          showTime
          disabled={[(isEdit || isView) && unStart, true]}
          onChange={([startTime, endTime]) => updatePrize({
            prizeStartTime: startTime.format('YYYY-MM-DD HH:mm:ss'),
            prizeEndTime: endTime.format('YYYY-MM-DD HH:mm:ss'),
          })}
        />
        <div className="form-extra">公示时间至少在活动结束时间之前48小时</div>
      </Form.Item>
      <Form.Item
        label="奖品列表"
        required
        validateState={hasPrizeError ? 'error' : undefined}
        help={hasPrizeError ? errors.find(err => err.includes('奖品')) : undefined}
      >
        <Box margin={[6, 0, 0, 0]}>
          <PrizeSendTable
            activityId={baseInfo.activityId}
            disabledTabs={receiveType === 1 ? [6] : [3, 6, 9]}
            status={[activityStatus, operationType]}
            prizeList={prize.prizeList}
            onPrizeChange={(newPrizeList) => updatePrize({ prizeList: newPrizeList })}
            maxLength={1}
            showLimit={false}
          />
          <div className="form-extra">注：虚拟奖项一旦开奖自动发放，实物奖项需要用户在中奖后主动领取（需填写收货地址）</div>
        </Box>
      </Form.Item>
    </Container>
  );
}

