import { AppState } from '@/pages/activity/10003/create/type';
import { PrizeTypeEnum } from '@/utils';
import dayjs from 'dayjs';

/**
 * 验证奖品设置
 * @param state 奖品设置数据
 * @returns 错误信息数组
 */
export function validatePrize(state: AppState): string[] {
    const errors: string[] = [];
    const { prize, base, receive, extra } = state;
    const { prizeList, prizeStartTime, prizeEndTime } = prize;
    const { startTime: baseStartTime, endTime: baseEndTime } = base;
    const { receiveType } = receive;
    const { operationType, activityStatus } = extra;

    const isCreate = operationType === 'add';
    const isEdit = operationType === 'edit';


    const checkPrizeStartTime = isCreate || (isEdit && activityStatus === 1);

    const validPrize = prizeList.filter(item => item.lotteryType !== PrizeTypeEnum.THANKS.value);
    if (validPrize.length === 0) {
      errors.push('请至少添加一个奖品');
    }

    if (receiveType === 2) {
      const errorPrizeTypes = [PrizeTypeEnum.COUPON.value, PrizeTypeEnum.MEMBER_POINT.value];
      // 自动领奖时 不应该有积分和优惠券
      const prizeType = validPrize[0]?.lotteryType;
      if (errorPrizeTypes.includes(prizeType)) {
        errors.push('配置自动领奖时，奖品不应该包含积分、优惠券');
      }
    }


    if (!prizeStartTime || !new Date(prizeStartTime).getTime()) {
      errors.push('请设置开奖公示开始时间');
    }
    // 开奖公示开始时间应该晚于当前时间
    if (prizeStartTime && checkPrizeStartTime) {
      const prizeStart = dayjs(prizeStartTime);
      const now = dayjs();
      if (prizeStart.isBefore(now)) {
        errors.push('开奖公示开始时间应该晚于当前时间');
      }
    }
    // 不可早于活动开始时间  and  至少在公示结束时间之前48小时
    if (prizeStartTime && baseStartTime) {
      const prizeStart = dayjs(prizeStartTime);
      const activityStart = dayjs(baseStartTime);
      if (prizeStart.isBefore(activityStart)) {
        errors.push('开奖公示开始时间不可早于活动开始时间');
      }
    }
    if (prizeEndTime && baseEndTime) {
      const prizeStart = dayjs(prizeStartTime);
      const prizeEnd = dayjs(prizeEndTime);
      if (prizeStart.isAfter(prizeEnd.subtract(48, 'hour'))) {
        errors.push('开奖公示结束时间至少要在活动结束时间之前48小时');
      }
    }

    return errors;
}

