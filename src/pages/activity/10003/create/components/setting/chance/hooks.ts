import { useActivity } from '@/pages/activity/10003/create/reducer';
import { validateChance } from './validator';
import { ChanceState, ModuleType } from '@/pages/activity/10003/create/type';


/**
 * 抽奖机会模块的自定义Hook
 * 提供对抽奖机会状态的访问和更新方法
 */
export function useChance() {
  const { state, dispatch } = useActivity();

  // 更新抽奖机会并自动重新验证
  const updateChance = (data?: Partial<ChanceState>, validate = true) => {
    if (!data) return;
    // 先更新数据
    dispatch({
      type: 'UPDATE_CHANCE',
      payload: data,
    });

    // 获取更新后的数据
    const updatedStateInfo = {
      ...state,
      chance: {
        ...state.chance,
        ...data,
      },
    };
    if (!validate) return;

    // 重新验证并更新错误状态
    const errors = validateChance(updatedStateInfo);
    dispatch({
      type: 'VALIDATE_MODULE',
      module: ModuleType.CHANCE,
      payload: errors,
    });
  };

  return {
    chance: state.chance,
    errors: state.errors[ModuleType.CHANCE] || [],
    updateChance,
    dispatch,
  };
}
