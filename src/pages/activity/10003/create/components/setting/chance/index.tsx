import Container from '@/components/Container';
import NumberInput from '@/components/NumberInput/NumberInput';
import { openTaskDialog, TaskTypeLabel } from '@/components/Task/util';
import TaskTable from '@/components/TaskTable';
import { Button, Form } from '@alifd/next';
import { useChance } from './hooks';
import { useActivity } from '../../../reducer';
import { ACTIVITY_STATUS } from '@/utils/constant';

export default function Chance() {
  const { chance, updateChance, errors } = useChance();
  const { freeChance, taskList } = chance;

  const { state } = useActivity();
  const { operationType, activityStatus } = state.extra;

  const isEdit = operationType === 'edit';
  const isView = operationType === 'view';
  const unStart = activityStatus !== ACTIVITY_STATUS.NOT_STARTED;

  const needDisable = (isEdit || isView) && unStart;

  const applyList = [1, 9, 10, 11];
  const taskApply = Object.keys(TaskTypeLabel).filter(key => applyList.includes(Number(key))).map(Number);

  const addTask = async () => {
    const result = await openTaskDialog({ taskList, apply: taskApply, unit: '张', unitName: '抽签码', activityType: 10003 });
    updateChance({ taskList: [...taskList, result] });
  };

  const handleTaskListChange = (newTaskList: any[]) => {
    updateChance({ taskList: newTaskList });
  };

  const hasTaskError = errors.find(error => error.includes('任务'));
  return (
    <Container title="获取抽签码">
      <Form.Item label="免费赠送" disabled={isView}>
        报名活动即赠送
        <NumberInput
          min={0}
          max={99}
          style={{ width: 80, marginTop: 10, margin: '0 4px' }}
          value={freeChance}
          onChange={(value: number) => updateChance({ freeChance: value })}
        />张抽签码
      </Form.Item>
      <Form.Item label="任务获取" required>
        <Button
          disabled={taskList.length >= taskApply.length}
          style={{ width: 100, float: 'left' }}
          type={'primary'}
          onClick={addTask}
        >添加任务({taskList.length}/{taskApply.length})</Button>
      </Form.Item>
      <Form.Item
        label=" "
        validateState={hasTaskError ? 'error' : undefined}
        help={hasTaskError ? errors.find(error => error.includes('任务')) : undefined}
      >
        <TaskTable
          unit={'张'}
          unitName={'抽签码'}
          disabled={needDisable}
          dataSource={taskList}
          activityType={10003}
          onChange={handleTaskListChange}
        />
      </Form.Item>
    </Container>
  );
}
