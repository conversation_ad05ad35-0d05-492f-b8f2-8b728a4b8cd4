import Container from '@/components/Container';
import { Form, Radio } from '@alifd/next';
import { useReceive } from './hooks';
import HelpTooltip from '@/components/HelpTooltip/HelpTooltip';

/**
 * 基础信息模块
 * 包含活动名称、活动时间等基本配置
 */
export default function Receive() {
  const { receive, updateReceive } = useReceive();

  const { receiveType } = receive;

  const handleReceiveTypeChange = (value: number) => {
    updateReceive({ receiveType: value });
  };

  return (
    <Container title={'领奖规则'}>
      <Form.Item label="领奖方式" required>
        <Radio.Group
          value={receiveType}
          onChange={handleReceiveTypeChange}
        >
          <Radio value={1}>手动领奖 <HelpTooltip content={'用户达成任务条件后，需再次进入活动后领取奖品'} /></Radio>
          <Radio value={2}>自动领奖 <HelpTooltip content={'（1）用户达成任务条件后自动发放奖品，无需再次进入活动页面'} /></Radio>
        </Radio.Group>
      </Form.Item>
    </Container>
  );
}
