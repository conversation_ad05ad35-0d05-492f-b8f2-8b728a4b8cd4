import { useActivity } from '@/pages/activity/10003/create/reducer';
import { ReceiveState, ModuleType } from '@/pages/activity/10003/create/type';
import { validateReceive } from './validator';

/**
 * 奖品设置模块的自定义Hook
 * 提供对奖品设置状态的访问和更新方法
 */
export function useReceive() {
  const { state, dispatch } = useActivity();

  // 更新抽奖机会并自动重新验证
  const updateReceive = (data?: Partial<ReceiveState>, validate = true) => {
    if (!data) return;
    // 先更新数据
    dispatch({
      type: 'UPDATE_RECEIVE',
      payload: data,
    });

    // 获取更新后的数据
    const updatedStateInfo = {
      ...state,
      receive: {
        ...state.receive,
        ...data,
      },
    };
    if (!validate) return;

    // 重新验证并更新错误状态
    const errors = validateReceive(updatedStateInfo);
    dispatch({
      type: 'VALIDATE_MODULE',
      module: ModuleType.RECEIVE,
      payload: errors,
    });
  };

  return {
    receive: state.receive,
    errors: state.errors[ModuleType.RECEIVE] || [],
    updateReceive,
    dispatch,
  };
}
