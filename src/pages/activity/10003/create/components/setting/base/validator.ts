import { AppState } from '@/pages/activity/10003/create/type';
import dayjs from 'dayjs';

/**
 * 验证基础信息
 * @param state 基础信息数据
 * @returns 错误信息数组
 */
export function validateBaseInfo(state: AppState): string[] {
    const errors: string[] = [];
    const { base, extra } = state;
    const { operationType, originalEndTime } = extra;

    // 验证活动名称
    if (!base.activityName || base.activityName.trim() === '') {
        errors.push('活动名称不能为空');
    }

    // 验证活动时间
    if (!base.startTime || base.startTime === '') {
        errors.push('活动开始时间不能为空');
    }

    if (!base.endTime || base.endTime === '') {
        errors.push('活动结束时间不能为空');
    }

    // 确保两个时间都存在再进行比较
    if (base.startTime && base.endTime &&
        base.startTime !== '' && base.endTime !== '' &&
        new Date(base.startTime) >= new Date(base.endTime)) {
        errors.push('活动结束时间必须晚于开始时间');
    }

    if (operationType === 'edit' && dayjs(base.endTime).isBefore(dayjs(originalEndTime))) {
        errors.push(`活动结束时间不能早于原始活动结束时间${originalEndTime}`);
    }

    return errors;
}

