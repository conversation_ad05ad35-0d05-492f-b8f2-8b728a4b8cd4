import { useActivity } from '../../reducer';
import LotteryCode from './lotteryCode';

export default function Decorate({ goNextStep, goPrevStep }) {
  const { state } = useActivity();
  const { operationType } = state.extra;

  const isView = operationType === 'view';

  const needDisable = isView;

  const props = {
    goNextStep,
    goPrevStep,
    needDisable,
  };
  return (
    <div>
      <LotteryCode {...props} />
    </div>
  );
}