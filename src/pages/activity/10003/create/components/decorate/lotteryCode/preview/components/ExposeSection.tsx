import styles from '../index.module.scss';

interface RecommendGood {
  picUrl: string;
  name: string;
  sellNum: number;
}

interface ExposeSectionProps {
  main: any;
  recommendGoods: RecommendGood[];
}

export default function ExposeSection({ recommendGoods }: ExposeSectionProps) {
  return (
    <div className={styles.expose} x-if={recommendGoods.length > 0}>
      <img src={'https://novae.oss-cn-zhangjiakou.aliyuncs.com/vf/%E5%A4%A7%E8%BD%AC%E7%9B%98%E8%A3%85%E4%BF%AE/%E6%8E%A8%E8%8D%90%E5%95%86%E5%93%81%E6%A0%87%E9%A2%98.png'} className={styles.exposeTitle} alt="" />
      <div className={styles.exposeContent}>
        {recommendGoods.map((item, index) => (
          <div key={index} className={styles.exposeContentItem}>
            <div className={styles.exposeContentItemImg}>
              <img src={item.picUrl} alt="" />
            </div>
            <div className={styles.exposeContentItemName}>{item.name}</div>
            <div className={styles.exposeContentItemSellNum}>销量：<span>{item.sellNum}</span></div>
            <div className={styles.exposeContentItemBuy}>去购买</div>
          </div>
        ))}
      </div>
    </div>
  );
}