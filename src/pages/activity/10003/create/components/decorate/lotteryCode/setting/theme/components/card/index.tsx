import { Box, Message } from '@alifd/next';
import { CardState } from '../../../../type';
import styles from './index.module.scss';
import ImgUpload from '@/components/ImgUpload';
import ColorPickerFormItem from '@/components/ColorPickerFormItem/ColorPickerFormItem';
import { useEffect, useMemo } from 'react';

export default function TurnTableCard(props) {
  const { tState, tDispatch, state, disabled } = props;
  const { card } = tState;
  const { base } = state;
  const { startTime, endTime, activityName } = base;
  const { cardBg, countDownColor } = card;

  const countDown = useMemo(() => {
    const now = new Date().getTime();
    const end = new Date(endTime).getTime();
    const diff = end - now;
    const days = Math.floor(diff / (1000 * 60 * 60 * 24));
    const hours = Math.floor((diff % (1000 * 60 * 60 * 24)) / (1000 * 60 * 60));
    const minutes = Math.floor((diff % (1000 * 60 * 60)) / (1000 * 60));
    const daysStr = days.toString().padStart(2, '0');
    const hoursStr = hours.toString().padStart(2, '0');
    const minutesStr = minutes.toString().padStart(2, '0');
    return { days: daysStr, hours: hoursStr, minutes: minutesStr };
  }, [startTime, endTime]);

  const handleUpdateField = (field: keyof CardState, value: any) => {
    tDispatch({ type: 'UPDATE_MODULE', payload: { [field]: value } });
  };

  useEffect(() => {
    tDispatch({
      type: 'UPDATE_THEME',
      payload: 'card',
    });
  }, []);
  return (
    <Box direction="row" spacing={16} padding={[20, 0, 0]}>
      <Box direction="column" spacing={16} flex={3}>
        <Message
          type="warning"
        >
          <div style={{ fontSize: 14 }}>通用列表入口说明：</div>
          <p>
            通用列表入口：指的是活动的入口集合页，在活动推广时可将多个活动入口卡片集合在一个页面上由用户自行选择想要进入的活动</p>
          <div style={{ fontSize: 14 }}>使用说明：</div>
          <p>在此处配置活动入口样式后，可在【活动推广-通用列表页】中配置投放活动，用户进入后可根据千人千面条件查看对应活动入口</p>
        </Message>
        <ImgUpload
          label="卡片背景"
          required
          img={{
            value: cardBg,
            width: 726,
            height: 346,
          }}
          onSuccess={(url: string) => handleUpdateField('cardBg', url)}
          onReset={() => handleUpdateField('cardBg', -1)}
          disabled={disabled}
        />
        <ColorPickerFormItem
          label="倒计时文字色"
          color={{ value: countDownColor }}
          onSetColor={(color: string) => handleUpdateField('countDownColor', color)}
          onReset={() => handleUpdateField('countDownColor', -1)}
          disabled={disabled}
        />
      </Box>
      <Box flex={2}>
        <div className={styles.card} style={{ '--count-down-color': countDownColor } as React.CSSProperties}>
          <img className={styles.cardBg} src={cardBg} alt="" />
          <div className={styles.activityName}>
            <span className={styles.activityNameText}>{activityName}</span>
          </div>
          <div className={styles.countDown}>
            距离活动结束还有
            <span className={styles.countDownTextUnit}>{countDown.days}</span>
            天
            <span className={styles.countDownTextUnit}>{countDown.hours}</span>
            时
            <span className={styles.countDownTextUnit}>{countDown.minutes}</span>
            分
          </div>
        </div>
      </Box>
    </Box>
  );
}
