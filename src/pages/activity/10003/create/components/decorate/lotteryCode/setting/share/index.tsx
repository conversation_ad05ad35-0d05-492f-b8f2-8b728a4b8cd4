import { Box, Form, Input } from '@alifd/next';
import { useShare } from './hook';
import ImgUpload from '@/components/ImgUpload';
import { useActivity } from '@/pages/activity/10003/create/reducer';

export default function BlindBoxShare(props) {
  const { disabled } = props;
  const { share, errors, updateShare } = useShare();

  const { state } = useActivity();

  const { extra } = state;

  const { shareContent, shareTitle, mpImg } = share;

  const hasTitleError = errors.some(err => err.includes('分享标题'));
  const hasContentError = errors.some(err => err.includes('分享内容'));
  const hasImgError = errors.some(err => err.includes('分享图片'));

  return (
    <Box margin={[16, 0, 0, 0]}>
      <Form.Item
        label={'分享标题'}
        required
        disabled={disabled}
        validateState={hasTitleError ? 'error' : undefined}
        help={hasTitleError ? errors.find(err => err.includes('分享标题')) : undefined}
      >
        <Input
          value={shareTitle}
          style={{ width: 400 }}
          onChange={(value: string) => updateShare({ shareTitle: value })}
          maxLength={20}
          showLimitHint
        />
      </Form.Item>

      <Form.Item
        label={'分享内容'}
        required
        disabled={disabled}
        validateState={hasContentError ? 'error' : undefined}
        help={hasContentError ? errors.find(err => err.includes('分享内容')) : undefined}
      >
        <Input
          value={shareContent}
          style={{ width: 400 }}
          onChange={(value: string) => updateShare({ shareContent: value })}
          maxLength={50}
          showLimitHint
        />
      </Form.Item>
      <ImgUpload
        label="分享图"
        required
        disabled={disabled}
        validateState={hasImgError ? 'error' : undefined}
        help={hasImgError ? errors.find(err => err.includes('分享图片')) : undefined}
        img={{
          value: mpImg,
          width: 512,
          height: 512,
        }}
        onSuccess={(url: string) => updateShare({ mpImg: url })}
        onReset={() => updateShare({ mpImg: extra.mpImg })}
      />
    </Box>
  );
}