.main {
  position: relative;

  .kvContainer {
    min-height: 240px;

    .kv {
      width: 100%;
      display: block;
    }
  }

  .btns {
    position: absolute;
    right: 0;
    top: 170px;

    div {
      background-color: var(--ruleBtnBg);
      color: var(--ruleBtnText);
      border-top-left-radius: 10px;
      border-bottom-left-radius: 10px;
      margin-bottom: 8px;
      padding: 2px 8px;
      font-size: 10px;
    }
  }

  .countDown {
    position: relative;
    margin-top: -15px;


    .countDownImg {
      display: block;
      width: 95%;
      margin: 0 auto;
    }

    .countDownText {
      position: absolute;
      top: 50%;
      left: 85px;
      transform: translateY(-50%);
      font-size: 12px;
      color: #000000;
      display: flex;

      div {
        background-color: #000000;
        color: #fff;
        margin: 0 5px;
        padding: 2px 1px;
        border-radius: 2px;
        display: flex;
        justify-content: center;
        align-items: center;
        width: 16px;
        height: 16px;
      }
    }
  }

  .prizeInfo {
    position: relative;

    .prizeBg {
      display: block;
      width: 95%;
      margin: 0 auto;
      margin-top: 15px;
    }

    .prizeMain {
      position: absolute;
      top: 33px;
      display: flex;
      justify-content: center;
      width: 100%;

      .prizeMainImg {
        position: relative;

        .showImage {
          display: block;
          width: 110px;
          height: 110px;
          border-radius: 10px;
        }

        .prizeNum {
          position: absolute;
          bottom: 5px;
          left: 5px;
          font-size: 10px;
          zoom: .9;
          color: #fff;
          font-weight: 300;
          background-color: rgba(0, 0, 0, 0.5);
          padding: 0px 5px;
          border-radius: 3px;
        }
      }


    }

    .winImg {
      display: block;
      width: 50px;
      position: absolute;
      right: 35px;
      bottom: 15px;
    }

    .prizeInfoText {
      position: absolute;
      left: 24px;
      top: 150px;
      padding: 0 8px;


      .prizeName {
        width: 180px;
        height: 20px;
        font-size: 10px;
        line-height: 10px;
        overflow: hidden;
        text-overflow: ellipsis;
        display: -webkit-box;
        -webkit-line-clamp: 2;
        -webkit-box-orient: vertical;
        line-clamp: 2;
        letter-spacing: 1px;
        word-break: break-all;
      }

      .prizePrice {
        width: 225px;
        font-size: 10px;
        color: #000;
        text-align: right;

        .prizePriceText {
          font-size: 10px;
          color: #000;
          font-weight: bold;
          letter-spacing: 0.5px;
          zoom: .9;
        }

        .prizePriceValue {
          margin-left: 4px;
          font-size: 16px;
          color: #000;
          font-weight: bold;
          font-weight: 400;
        }
      }

    }

    .prizeBtn {
      position: absolute;
      bottom: 60px;
      left: 50%;
      transform: translateX(-50%);
      width: 244px;
      height: 35px;
      background-size: contain;
      background-repeat: no-repeat;
      display: flex;
      justify-content: center;
      align-items: center;

      .prizeBtnText {
        font-size: 15px;
        color: #fff;
        font-weight: 500;
      }
    }

    .prizeInfoTag {
      position: absolute;
      bottom: 82px;
      font-size: 10px;
      padding: 3px 6px;
      right: 25px;
      background: #f7af41;
      color: #000;
      border-top-right-radius: 10px;
      border-top-left-radius: 10px;
      border-bottom-left-radius: 10px;
      font-weight: bold;

      span {
        zoom: .8;
      }
    }

  }


  .code {
    margin-top: 15px;
    position: relative;

    .codeListBg {
      display: block;
      width: 95%;
      margin: 0 auto;
    }

    .codeTitle {
      position: absolute;
      top: 0px;
      left: 10px;
      width: 110px;
      height: 28px;
      white-space: nowrap;
      overflow: hidden;
      text-overflow: ellipsis;
      line-height: 28px;
      padding: 0 5px;
      font-size: 13px;
      font-weight: 500;
      text-align: center;
    }

    .codeBtn {
      position: absolute;
      top: 9px;
      right: 30px;

      img {
        height: 11px;
      }
    }

    .codeList {
      position: absolute;
      bottom: 0px;
      top: 27px;

      display: grid;
      grid-template-columns: repeat(3, 1fr);
      gap: 5px;
      width: 95%;
      padding: 12px 8px;
      left: 50%;
      transform: translateX(-50%);

      .codeItem {
        background: #f2f3f7;
        color: #000;
        display: flex;
        justify-content: center;
        align-items: center;
        font-size: 12px;
        border-radius: 5px;
      }

      .codeItem1 {
        display: flex;
        justify-content: center;
        align-items: center;
        font-size: 12px;
      }
    }
  }


  .expose {
    width: 95%;
    text-align: center;
    margin: 15px auto;


    .exposeTitle {
      height: 20px;
    }

    .exposeContent {
      display: grid;
      grid-template-columns: repeat(2, 1fr);
      gap: 6px;
      width: 100%;
      margin-top: 6px;

      height: 450px;
      overflow-y: scroll;


      .exposeContentItem {
        padding: 5px;
        width: 134px;
        height: 220px;
        background: #ecedee;


        .exposeContentItemImg {
          width: 95%;
          object-fit: contain;
          background: white;
          margin: 0 auto;

          img {
            width: 100%;
            display: block;
          }
        }

        .exposeContentItemName {
          width: 100%;
          height: 30px;
          font-size: 12px;
          text-align: left;
          color: #333;
          max-width: 100%;
          overflow: hidden;
          text-overflow: ellipsis;
          display: -webkit-box;
          line-clamp: 2;
          -webkit-line-clamp: 2;
          -webkit-box-orient: vertical;
          margin-top: 5px;
        }

        .exposeContentItemSellNum {
          text-align: left;
          font-size: 10px;
          font-weight: 500;
          color: #795500;

          span {
            font-size: 18px;
          }
        }

        .exposeContentItemBuy {
          margin: 5px auto 0;
          width: 100px;
          height: 25px;
          border-radius: 25px;
          background: #000;
          line-height: 25px;
          color: #fff;
          font-size: 13px;
        }
      }
    }
  }
}

.popup {
  position: absolute;
  top: 0;
  bottom: 0;
  left: 0;
  right: 0;
  background: rgba(0, 0, 0, 0.6);
  z-index: 10000;

  .popupBg {
    width: 250px;
    object-fit: contain;
    position: absolute;
    left: 50%;
    top: 160px;
    transform: translateX(-50%);
  }

  .popupInfo {
    width: 200px;
    position: absolute;
    top: 230px;
    left: 50%;
    z-index: 100001;
    transform: translateX(-50%);
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;

    .popupInfoTitle {
      font-size: 20px;
    }

    .popupInfoDesc {
      font-size: 13px;
      color: #808285;
      margin-top: 20px;
    }

    .popupBtn {
      width: 150px;
      height: 30px;
      color: var(--popupBtnText);
      font-size: 14px;
      display: flex;
      justify-content: center;
      align-items: center;
      margin-top: 50px;
      border-radius: 5px;
      background-color: var(--popupBtnBg);
    }
  }
}