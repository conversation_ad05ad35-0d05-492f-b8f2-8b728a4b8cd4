export interface ThemeState {
  current: 'main' | 'popup';
  main: MainState;
  popup: PopupState;
  card: CardState;
}

export interface CardState {
  cardBg: string;
  countDownColor: string;
}

export interface MainState {
  // 活动kv
  kv: string;
  // 规则按钮文字色
  ruleBtnText: string;
  // 规则按钮背景色
  ruleBtnBg: string;
  // 页面背景图
  pageBg: string;
  // 报名按钮
  signBtn: string;
  // 抽奖按钮文字色
  signText: string;
}

export interface PopupState {
  // 弹窗背景图
  popupBg: string;
  // 弹窗按钮背景色
  popupBtnBg: string;
  // 弹窗按钮文字色
  popupBtnText: string;

  // 浮动弹窗背景图
  floatBg: string;
  // 浮动弹窗顶部标题
  floatTitle: string;
  // 浮层内容主标题
  floatConentTitle: string;
  // 浮层内容说明文案
  floatConentText: string;

  // 子任务背景图
  taskBg: string;
  // 引导按钮背景
  taskGuideBtnBg: string;
  // 引导按钮文字色
  taskGuideText: string;

  // 领取记录背景图
  receiveBg: string;
  // 收货地址背景色
  addressBg: string;
  // 收货地址文字色
  addressText: string;
}

type ThemeActionType = 'UPDATE_MODULE' | 'INIT_MODULE' | 'SET_CURRENT_MODULE' | 'UPDATE_THEME';

// 动作类型定义
export type UpdateModuleAction = {
  type: ThemeActionType;
  module?: 'main' | 'popup';
  payload?: Partial<ThemeState>;
};

export type ThemeAction = UpdateModuleAction;