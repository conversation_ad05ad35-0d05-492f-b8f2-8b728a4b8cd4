import ImgUpload from '@/components/ImgUpload';
import { Box } from '@alifd/next';
import { MainState } from '../../../../type';
import ColorPickerFormItem from '@/components/ColorPickerFormItem/ColorPickerFormItem';

export default function Main(props) {
  const { tState, tDispatch, needDisable } = props;
  const { main } = tState;
  const {
    kv,
    ruleBtnText,
    ruleBtnBg,
    pageBg,
    signBtn,
    signText,
  } = main;

  const handleUpdateField = (field: keyof MainState, value: any) => {
    tDispatch({ type: 'UPDATE_MODULE', payload: { [field]: value } });
  };

  return (
    <Box direction="row" spacing={32}>
      <Box direction="column" spacing={16} margin={[16, 0, 0, 0]}>
        <ImgUpload
          label="活动KV图"
          required
          disabled={needDisable}
          img={{
            value: kv,
            width: 750,
          }}
          onSuccess={(url: string) => handleUpdateField('kv', url)}
          onReset={() => handleUpdateField('kv', -1)}
        />
        <ColorPickerFormItem
          label="规则按钮字色"
          disabled={needDisable}
          color={{ value: ruleBtnText }}
          onSetColor={(color: string) => handleUpdateField('ruleBtnText', color)}
          onReset={() => handleUpdateField('ruleBtnText', -1)}
        />
        <ColorPickerFormItem
          label="规则按钮背景色"
          disabled={needDisable}
          color={{ value: ruleBtnBg }}
          onSetColor={(color: string) => handleUpdateField('ruleBtnBg', color)}
          onReset={() => handleUpdateField('ruleBtnBg', -1)}
        />
        <ColorPickerFormItem
          label="页面背景色"
          disabled={needDisable}
          color={{ value: pageBg }}
          onSetColor={(color: string) => handleUpdateField('pageBg', color)}
          onReset={() => handleUpdateField('pageBg', -1)}
        />

      </Box>
      <span />
      <Box direction="column" spacing={16} margin={[16, 0, 0, 0]}>

        <ImgUpload
          label="报名主图按钮背景"
          required
          disabled={needDisable}
          img={{
            value: signBtn,
            width: 604,
            height: 88,
          }}
          onSuccess={(url: string) => handleUpdateField('signBtn', url)}
          onReset={() => handleUpdateField('signBtn', -1)}
        />
        <ColorPickerFormItem
          label="报名主按钮字色"
          disabled={needDisable}
          color={{ value: signText }}
          onSetColor={(color: string) => handleUpdateField('signText', color)}
          onReset={() => handleUpdateField('signText', -1)}
        />

      </Box>
    </Box>
  );
}