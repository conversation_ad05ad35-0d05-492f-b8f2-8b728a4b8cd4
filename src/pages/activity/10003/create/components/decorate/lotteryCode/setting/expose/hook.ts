import { useActivity } from '@/pages/activity/10003/create/reducer';
import { validateRecommendGoods } from './validator';
import { ModuleType, SkuItem } from '@/pages/activity/10003/create/type';


/**
 * 推荐商品模块的自定义Hook
 * 提供对推荐商品状态的访问和更新方法
 */
export function useRecommendGoods() {
  const { state, dispatch } = useActivity();

  // 更新推荐商品并自动重新验证
  const updateRecommendGoods = (data?: SkuItem[]) => {
    if (!data) return;
    // 先更新数据
    dispatch({
      type: 'UPDATE_RECOMMEND_GOODS',
      payload: data,
    });

    // 获取更新后的数据
    const updatedRecommendGoods = {
      ...state,
      recommendGoods: data,
    };

    // 重新验证并更新错误状态
    const errors = validateRecommendGoods(updatedRecommendGoods);
    dispatch({
      type: 'VALIDATE_MODULE',
      module: ModuleType.RECOMMEND_GOODS,
      payload: errors,
    });
  };

  return {
    recommendGoods: state.recommendGoods,
    errors: state.errors[ModuleType.RECOMMEND_GOODS] || [],
    updateRecommendGoods,
    dispatch,
  };
}
