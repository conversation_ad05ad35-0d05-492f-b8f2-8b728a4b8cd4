import { Box } from '@alifd/next';
import Main from './components/main';
import { useEffect } from 'react';

export default function BlindBoxTheme(props) {
  const { tDispatch } = props;


  useEffect(() => {
    tDispatch({
      type: 'UPDATE_THEME',
      payload: 'main',
    });
  }, []);
  return (
    <Box direction="column" spacing={16} margin={[16, 0, 0, 0]}>
      <Main {...props} />
    </Box>
  );
}