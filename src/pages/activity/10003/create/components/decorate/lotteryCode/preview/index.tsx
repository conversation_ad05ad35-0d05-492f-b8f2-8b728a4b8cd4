import PhonePreview from '@/components/PhonePreview/PhonePreview';
import styles from './index.module.scss';
import { useEffect, useState } from 'react';
import ExposeSection from './components/ExposeSection';
import Popup from './components/Popup';
import { createMainStyles, scrollToTop } from './utils/styleUtils';
import dayjs from 'dayjs';
import duration from 'dayjs/plugin/duration';

// 扩展dayjs以使用duration插件
dayjs.extend(duration);

// 补0工具函数
const padZero = (num: number, type?: string): string => {
  if (type === 'day' && num === 0) {
    return '0';
  }
  return num.toString().padStart(2, '0');
};

export default function BlindBoxPreview(props) {
  const { tState, state } = props;
  const { main, popup, current } = tState;
  const { recommendGoods, prize } = state;

  const [timeInfo, setTimeInfo] = useState({
    days: '0',
    hours: '00',
    minutes: '00',
    seconds: '00',
  });

  const { prizeEndTime, prizeList } = prize;

  const prizeInfo = prizeList[0];

  useEffect(() => {
    if (current === 'popup') {
      scrollToTop();
    }
  }, [current]);

  useEffect(() => {
    const calculateTimeRemaining = () => {
      const now = dayjs();
      const endTime = dayjs(prizeEndTime);
      const diff = endTime.diff(now);

      if (diff <= 0) {
        setTimeInfo({ days: '0', hours: '00', minutes: '00', seconds: '00' });
        return;
      }

      const duration = dayjs.duration(diff);
      const days = Math.floor(duration.asDays());
      const hours = duration.hours();
      const minutes = duration.minutes();
      const seconds = duration.seconds();

      setTimeInfo({
        days: padZero(days, 'day'),
        hours: padZero(hours),
        minutes: padZero(minutes),
        seconds: padZero(seconds),
      });
    };

    // 初始计算
    calculateTimeRemaining();

    // 设置定时器，每秒更新一次
    const timer = setInterval(calculateTimeRemaining, 1000);

    // 清理定时器
    return () => clearInterval(timer);
  }, [prizeEndTime]);


  return (
    <PhonePreview
      width={290}
    >
      <div style={{ position: 'relative' }}>
        <div
          style={createMainStyles(main)}
          className={styles.main}
        >
          <div className={styles.kvContainer}>
            <img className={styles.kv} src={main.kv} alt="" />
          </div>
          <div className={styles.btns}>
            <div>活动规则</div>
            <div>领奖记录</div>
          </div>

          <div className={styles.countDown}>
            <img className={styles.countDownImg} src={'https://novae.oss-cn-zhangjiakou.aliyuncs.com/vf/%E6%8A%BD%E7%AD%BE%E7%A0%81/%E8%B7%9D%E7%A6%BB%E5%BC%80%E5%A5%96.png'} alt="" />
            <div className={styles.countDownText}>
              距开奖还有
              <div>{timeInfo.days}</div>
              天
              <div>{timeInfo.hours}</div>
              :
              <div>{timeInfo.minutes}</div>
            </div>

          </div>
          <div className={styles.prizeInfo}>
            <img className={styles.prizeBg} src="https://novae.oss-cn-zhangjiakou.aliyuncs.com/vf/%E6%8A%BD%E7%AD%BE%E7%A0%81/%E5%A5%96%E5%93%81%E5%B1%95%E7%A4%BA%E8%83%8C%E6%99%AF.png" alt="" />
            <div className={styles.prizeMain}>
              <div className={styles.prizeMainImg}>
                <img className={styles.showImage} src={prizeInfo.showImage} alt="" />
                <div className={styles.prizeNum}>仅{prizeInfo.prizeNum}份</div>
              </div>
              <img className={styles.winImg} src="https://novae.oss-cn-zhangjiakou.aliyuncs.com/vf/%E6%8A%BD%E7%AD%BE%E7%A0%81/%E6%81%AD%E5%96%9C%E4%B8%AD%E5%A5%96.png" alt="" />
            </div>
            <div className={styles.prizeInfoText}>
              <div className={styles.prizeName}>{prizeInfo.lotteryName}</div>
              <div className={styles.prizePrice}>
                <span className={styles.prizePriceText}>价值 ¥</span>
                <span className={styles.prizePriceValue}>{prizeInfo.price}</span>
              </div>
            </div>
            <div className={styles.prizeBtn} style={{ backgroundImage: `url(${main.signBtn})` }}>
              <div className={styles.prizeBtnText} style={{ color: main.signText }}>
                获取更多抽签码
              </div>
            </div>
            <div className={styles.prizeInfoTag}>
              <span>有签码可领</span>
            </div>

          </div>

          <div className={styles.code}>
            <img className={styles.codeListBg} src="https://novae.oss-cn-zhangjiakou.aliyuncs.com/vf/%E6%8A%BD%E7%AD%BE%E7%A0%81/%E6%8A%BD%E7%AD%BE%E7%A0%81%E5%B1%95%E7%A4%BA%E8%83%8C%E6%99%AF.png" alt="" />
            <div className={styles.codeTitle}>我的抽签码(100)</div>
            <div className={styles.codeBtn}>
              <img src="https://novae.oss-cn-zhangjiakou.aliyuncs.com/vf/%E6%8A%BD%E7%AD%BE%E7%A0%81/%E5%85%A8%E9%83%A8%E6%8A%BD%E7%AD%BE%E7%A0%81.png" alt="" />
            </div>
            <div className={styles.codeList}>
              <div className={styles.codeItem}>SEKHR984</div>
              <div className={styles.codeItem}>SEKHR984</div>
              <div className={styles.codeItem}>SEKHR984</div>
              <div className={styles.codeItem}>SEKHR984</div>
              <div className={styles.codeItem}>SEKHR984</div>
              <div className={styles.codeItem}>SEKHR984</div>
              <div className={styles.codeItem}>SEKHR984</div>
              <div className={styles.codeItem}>SEKHR984</div>
              <div className={styles.codeItem1}>······</div>
            </div>
          </div>
          <ExposeSection main={main} recommendGoods={recommendGoods} />
        </div>

        <Popup popup={popup} visible={current === 'popup'} />
      </div>
    </PhonePreview>
  );
}