import { Tab } from '@alifd/next';
import BlindBoxTheme from './theme';
import BlindBoxExpose from './expose';
import BlindBoxShare from './share';
import BlindBoxCard from './theme/components/card';
import { useState } from 'react';

export default function BlindBoxSetting(props) {
  const { needDisable } = props;
  const [activeKey, setActiveKey] = useState('1');
  return (
    <Tab
      activeKey={activeKey}
      onChange={(key) => {
      setActiveKey(key);
    }}
    >
      <Tab.Item key={'1'} title={'页面样式装修'}>
        <BlindBoxTheme x-if={activeKey === '1'} disabled={needDisable} {...props} />
      </Tab.Item>
      <Tab.Item key={'2'} title={'推荐商品设置'}>
        <BlindBoxExpose x-if={activeKey === '2'} disabled={needDisable} />
      </Tab.Item>
      <Tab.Item key={'3'} title={'通用列表页设置'}>
        <BlindBoxCard x-if={activeKey === '3'} disabled={needDisable} {...props} />
      </Tab.Item>
      <Tab.Item key={'4'} title={'分享设置'}>
        <BlindBoxShare x-if={activeKey === '4'} disabled={needDisable} />
      </Tab.Item>
    </Tab>
  );
}