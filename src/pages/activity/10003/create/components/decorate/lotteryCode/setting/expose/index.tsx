import ProductPicker from '@/components/ProductPicker';
import { SkuItem } from '@/pages/activity/10003/create/type';
import { Box, Button, Icon } from '@alifd/next';
import { useRecommendGoods } from './hook';


export default function BlindBoxExpose(props) {
  const { recommendGoods, updateRecommendGoods, errors } = useRecommendGoods();

  const { disabled } = props;

  const handleDeleteProduct = (productId: string) => {
    updateRecommendGoods(recommendGoods.filter(product => product.numIid !== productId));
  };

  const hasError = errors.some(err => err.includes('推荐商品'));

  return (
    <Box direction="column" spacing={16} margin={[16, 0, 0, 0]}>
      <ProductPicker
        min={1}
        max={50}
        disabled={disabled}
        selectedItems={recommendGoods}
        onSelectedProducts={(selected: SkuItem[]) => updateRecommendGoods(selected)}
      />
      <div >
        <Box
          direction="row"
          wrap
          spacing={9}
          style={{ marginTop: 16 }}
        >
          {recommendGoods.map(product => (
            <div
              key={product.numIid}
              className={'product-item'}
            >
              <div className="product-img">
                <img
                  src={product.picUrl}
                  alt={product.name}
                />
                <Button
                  className="delete-product-item-icon"
                  type="primary"
                  text
                  disabled={disabled}
                  iconSize="small"
                  onClick={() => handleDeleteProduct(product.numIid)}
                >
                  <Icon type="ashbin" />
                </Button>
              </div>
              <span className="product-title">{product.name}</span>
              <div className="product-info">
                <span className="product-price" />
                <div className="product-stock">
                  销量
                  <span className="product-stock-num">{product.sellNum || 0}</span>
                </div>
              </div>
            </div>
          ))}
        </Box>
      </div>
      <div x-if={hasError} className="text-red">
        {
          errors.find(err => err.includes('推荐商品'))
        }
      </div>
    </Box>
  );
}
