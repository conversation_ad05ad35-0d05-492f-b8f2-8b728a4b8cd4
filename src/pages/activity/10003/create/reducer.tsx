import React, { createContext, useReducer, ReactNode, useContext } from 'react';
import { Action, AppState, ModuleType } from './type';
import dayjs from 'dayjs';
import { getExpireDay } from '@/utils';

const expireDay = getExpireDay();
// 初始状态
const initialState: AppState = {
  extra: {
    // 操作类型 add 新增 edit 编辑 view 预览 copy 复制
    operationType: 'add',
    // 原始结束时间 用于复制活动时，判断不可选择原始活动之前的时间
    originalEndTime: dayjs().add(expireDay > 15 ? 15 : expireDay, 'day').format('YYYY-MM-DD 00:00:00'),
    // 活动状态 1未开始 2进行中 3已结束
    activityStatus: 1,
    // 活动地址
    activityUrl: '',
    // 分享图 用户重置
    mpImg: 'https://novae.oss-cn-zhangjiakou.aliyuncs.com/vf/%E6%8A%BD%E7%AD%BE%E7%A0%81/%E9%BB%98%E8%AE%A4%E5%88%86%E4%BA%AB%E5%9B%BE.jpg',
  },
  base: {
    // 活动类型
    activityType: 10003,
    // 模板ID
    templateCode: 1001,
    // 活动名称
    activityName: `抽签赢好礼${dayjs().format('YYYY-MM-DD')}`,
    // 活动开始时间
    startTime: dayjs().format('YYYY-MM-DD 00:00:00'),
    // 活动结束时间
    endTime: dayjs().add(expireDay > 15 ? 15 : expireDay, 'day').format('YYYY-MM-DD 00:00:00'),
    // 活动ID
    activityId: '',
    path: 'lottery/10003/10003',
  },
  threshold: {
    thresholdType: 1,
    thresholdInfo: {
      crowdName: '',
      conditions: {},
    },
  },
  chance: {
    // 免费赠送次数
    freeChance: 0,
    taskList: [],
  },
  receive: {
    receiveType: 1,
  },
  prize: {
    prizeStartTime: dayjs().add(expireDay > 15 ? 13 : expireDay - 2, 'day').format('YYYY-MM-DD 00:00:00'),
    prizeEndTime: dayjs().add(expireDay > 15 ? 15 : expireDay, 'day').format('YYYY-MM-DD 00:00:00'),
    prizeList: [],
  },
  rule: '',
  recommendGoods: [],
  decorate: '',
  share: {
    shareTitle: '抽签码赢好礼，快来碰碰你的运气！',
    shareContent: '我抽中幸运大奖啦！赶快加入，说不定好运就是你的！',
    mpImg: 'https://novae.oss-cn-zhangjiakou.aliyuncs.com/vf/share.jpg',
  },
  errors: {
    [ModuleType.BASE]: [],
    [ModuleType.THRESHOLD]: [],
    [ModuleType.CHANCE]: [],
    [ModuleType.RECEIVE]: [],
    [ModuleType.PRIZE]: [],
    [ModuleType.RULE]: [],
    [ModuleType.SHARE]: [],
    [ModuleType.RECOMMEND_GOODS]: [],
  },
};


// Reducer函数
function reducer(state: AppState, action: Action): AppState {
  switch (action.type) {
    case 'UPDATE_BASE':
      return {
        ...state,
        base: {
          ...state.base,
          ...action.payload,
        },
      };
    case 'UPDATE_THRESHOLD':
      console.log('UPDATE_THRESHOLD', {
        ...state.threshold,
        ...action.payload,
      });
      return {
        ...state,
        threshold: {
          ...state.threshold,
          ...action.payload,
        },
      };
    case 'UPDATE_CHANCE':
      console.log('UPDATE_CHANCE', {
        ...state.chance,
        ...action.payload,
      });
      return {
        ...state,
        chance: {
          ...state.chance,
          ...action.payload,
        },
      };
    case 'UPDATE_RECEIVE':
      return {
        ...state,
        receive: {
          ...state.receive,
          ...action.payload,
        },
      };
    case 'UPDATE_PRIZE':
      console.log('UPDATE_PRIZE', action.payload);
      return {
        ...state,
        prize: {
          ...state.prize,
          ...action.payload,
        },
      };
    case 'UPDATE_RULE':
      return {
        ...state,
        rule: action.payload,
      };
    case 'UPDATE_SHARE':
      return {
        ...state,
        share: {
          ...state.share,
          ...action.payload,
        },
      };
    case 'UPDATE_RECOMMEND_GOODS':
      return {
        ...state,
        recommendGoods: action.payload,
      };
    case 'UPDATE_DECORATE':
      console.log('action.payload', action.payload);
      return {
        ...state,
        decorate: action.payload,
      };
    case 'VALIDATE_MODULE':
      if (!action.module) return state;
      return {
        ...state,
        errors: {
          ...state.errors,
          [action.module]: action.payload,
        },
      };
    case 'CLEAR_ERRORS':
      return {
        ...state,
        errors: {
          ...state.errors,
          [action.module as ModuleType]: [],
        },
      };
    case 'UPDATE_EXTRA':
      console.log('UPDATE_EXTRA', {
        extra: {
          ...state.extra,
          ...action.payload,
        },
      });
      return {
        ...state,
        extra: {
          ...state.extra,
          ...action.payload,
        },
      };
    case 'INIT_MODULE': {
      console.log('INIT_MODULE', {
        ...state,
        ...action.payload.activityData,
        decorate: action.payload.decorationData,
      });
      return {
        ...state,
        ...action.payload.activityData,
        decorate: action.payload.decorationData,
      };
    }
    default:
      return state;
  }
}

// 创建Context
interface ActivityContextType {
  state: AppState;
  dispatch: React.Dispatch<Action>;
}

export const ActivityContext = createContext<ActivityContextType>({
  state: initialState,
  dispatch: () => null,
});

// Context Provider组件
interface ActivityProviderProps {
  children: ReactNode;
}

export function ActivityProvider({ children }: ActivityProviderProps) {
  const [state, dispatch] = useReducer(reducer, initialState);

  return (
    <ActivityContext.Provider value={{ state, dispatch }}>
      {children}
    </ActivityContext.Provider>
  );
}

// 自定义Hook方便使用Context
export function useActivity() {
  const context = useContext(ActivityContext);
  if (!context) {
    // 改成中文
    throw new Error('useActivity必须使用ActivityProvider包裹');
  }
  return context;
}
