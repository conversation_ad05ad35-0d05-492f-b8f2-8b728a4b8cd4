import Container from '@/components/Container';
import HelpTooltip from '@/components/HelpTooltip/HelpTooltip';
import usePagination from '@/hooks/usePagination';
import { maskSensitiveInfo } from '@/utils';
import { Balloon, Box, Button, DatePicker2, Dialog, Input, Message, Pagination, Select, Table } from '@alifd/next';
import { useEffect, useState } from 'react';
import dayjs from 'dayjs';
import { dataGetDrawCode, dataGetInviteDetail } from '@/api/v10003';

const pageNum = 1;
const pageSize = 10;

export default function DrawData({ activityId }) {
  const defaultSearchParams = {
    activityId,
    openId: '',
    timeRange: [],
    nick: '',
    taskType: -2, // 任务类型 -2全部 其他参照任务组件
    startTime: '',
    endTime: '',
  };
  // 搜索相关
  const [searchParams, setSearchParams] = useState({
    ...defaultSearchParams,
    activityId,
  });

  // 表格相关
const [tableData, setTableData] = useState<any>([]);
const [loading, setLoading] = useState(false);

const [recordTable, setRecordTable] = useState<any>([]);
const [recordVisible, setRecordVisible] = useState(false);

// 通用的参数更新函数
const updateSearchParam = (key: string, value: any) => {
  setSearchParams(prev => ({ ...prev, [key]: value }));
};


const pagination = usePagination(pageNum, pageSize, async (current, size, params) => {
  await fetchData({
    pageNum: current,
    pageSize: size,
    ...params,
  });
});

const fetchData = async (params?: any) => {
  setLoading(true);
  try {
    const [start, end] = searchParams.timeRange || [];

    // 构建查询参数
    const queryParams = {
      ...searchParams,
      startTime: start ? dayjs(start).format('YYYY-MM-DD 00:00:00') : '',
      endTime: end ? dayjs(end).format('YYYY-MM-DD 23:59:59') : '',
      pageNum: pagination.current,
      pageSize: pagination.pageSize,
    };

    const { records, total }: any = await dataGetDrawCode({
      ...queryParams,
      ...params,
    });

    setTableData(records || []);
    pagination.setTotal(total || 0);
  } catch (e) {
    Message.error(e.message);
  } finally {
    setLoading(false);
  }
};

const handleSearch = () => {
  pagination.changePage(pageNum);
};

const handleReset = () => {
  setSearchParams(defaultSearchParams);
  pagination.reset(defaultSearchParams);
};

const renderNotes = (record: any) => {
  if (record.taskType === '商品下单') {
    return <div>{`关联订单号：${record.notes}`}</div>;
  }
  if (record.taskType === '邀请好友参与并入会') {
    return <div>{`被邀请人：${record.notes}`}</div>;
  }
  if (record.taskType === '邀请好友参与并下单') {
    return (
            record.needDetail
              ? <Button type={'primary'} text onClick={() => showRecord(record)}>查看被邀请人明细</Button> : <div>{record.notes}</div>
    );
  }
  return <div>{record.notes}</div>;
};

const showRecord = async (record) => {
  const data: any = await dataGetInviteDetail({
    activityId,
    openId: record.openId,
  });
  setRecordTable(data || []);
  setRecordVisible(true);
};

useEffect(() => {
  fetchData().then();
}, []);

  return (
    <Container style={{ marginTop: 16 }}>
      <Box direction="row" spacing={16} margin={[0, 0, 16, 0]} wrap align="center">
        <Input label={'用户昵称'} value={searchParams.nick} onChange={(value) => updateSearchParam('nick', value)} />
        <Input label={'openId'} value={searchParams.openId} onChange={(value) => updateSearchParam('openId', value)} />
        <DatePicker2.RangePicker
          type={'range'}
          label={'发放时间'}
          value={searchParams.timeRange}
          onChange={(value) => updateSearchParam('timeRange', value)}
        />
        <Select label={'任务类型'} value={searchParams.taskType} onChange={(value) => updateSearchParam('taskType', value)}>
          <Select.Option value={-2}>全部</Select.Option>
          <Select.Option value={-1}>报名活动赠送</Select.Option>
          <Select.Option value={1}>商品下单</Select.Option>
          <Select.Option value={9}>加入会员</Select.Option>
          <Select.Option value={10}>邀请好友参与并入会</Select.Option>
          <Select.Option value={11}>邀请好友参与并下单</Select.Option>
        </Select>
        <Button type="primary" onClick={handleSearch}>查询</Button>
        <Button onClick={handleReset}>重置</Button>
      </Box>
      <Table
        dataSource={tableData}
        loading={loading}
      >
        <Table.Column
          title="用户昵称"
          dataIndex="nickName"
          cell={(value) => {
            return <div >{value === '匿名' ? value : maskSensitiveInfo(value, 1, 1)}</div>;
        }}
        />
        <Table.Column
          title="openId"
          dataIndex="openId"
          cell={(value) => {
          return <div >{maskSensitiveInfo(value, 1, 1, 6)}</div>;
        }}
        />
        <Table.Column title="时间" dataIndex="createTime" cell={(value) => (value ? dayjs(value).format('YYYY-MM-DD HH:mm:ss') : '-')} />
        <Table.Column title="抽签码数量" dataIndex="drawCodeNum" />
        <Table.Column
          title="抽签码明细"
          dataIndex="drawCode"
          cell={
          (value, index, record) => {
            if (!value) return <div>-</div>;
            return (<div>
              <Balloon
                trigger={
                  <div
                    style={{
                          maxWidth: '180px',
                          overflow: 'hidden',
                          textOverflow: 'ellipsis',
                          whiteSpace: 'nowrap',
                          cursor: 'pointer',
                        }}
                  >
                    {value}
                  </div>
                  }
                closable={false}
                triggerType="hover"
              >
                <div style={{ maxWidth: '300px', wordBreak: 'break-all' }}>{value}</div>
              </Balloon>
            </div>);
          }
        }
        />
        <Table.Column title="完成任务类型" dataIndex="taskType" />
        <Table.Column
          title="任务明细"
          dataIndex="details"
          cell={(value) => {
          return <div>{value}</div>;
        }}
        />
        <Table.Column
          width={200}
          title="备注"
          dataIndex="notes"
          cell={(value, index, record) => renderNotes(record)}
        />
      </Table>
      <Box direction="row" justify="flex-end" margin={[16, 0, 0, 0]}>
        <Pagination
          {...pagination.paginationProps}
          shape={'arrow-only'}
          totalRender={(total) => `共 ${total} 条`}
          pageSizeSelector="dropdown"
          pageSizePosition="end"
        />
      </Box>
      <Dialog width={850} title="被邀请人明细" visible={recordVisible} v2 footer={false} onClose={() => setRecordVisible(false)}>
        <Table dataSource={recordTable}>
          <Table.Column width={200} title="被邀人openId" dataIndex="openId" />
          <Table.Column width={220} title="被邀人报名时间" dataIndex="signUpTime" cell={(value) => (value ? dayjs(value).format('YYYY-MM-DD HH:mm:ss') : '-')} />
          <Table.Column
            width={200}
            title="被邀人订单号"
            dataIndex="orderIds"
            cell={(value) => {
            return value ? value.split(',').map((item: string) => <div key={item}>{item}</div>) : '-';
          }}
          />
          <Table.Column width={220} title="被邀人累计订单金额" dataIndex="orderAmt" />
        </Table>
        <Box direction="row" justify="center" margin={[16, 0, 0, 0]}>
          <Button type="primary" onClick={() => setRecordVisible(false)}>我知道了</Button>
        </Box>
      </Dialog>
    </Container>
  );
}
