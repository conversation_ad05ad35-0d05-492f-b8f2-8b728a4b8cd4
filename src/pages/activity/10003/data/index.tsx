import { Tab } from '@alifd/next';
import ReceiveData from './receive';
import PrizeData from './prize';
import DrawData from './draw';
import ActivityData from './activity';
import AwardData from './award';
import { useLocation } from 'ice';
import { useState } from 'react';

export default function Activity10002Data() {
  const location = useLocation();
  const { record } = location.state || {};

  const { activityId, startTime, endTime } = record || {};
  const [activeTab, setActiveTab] = useState('receive');

  const props = {
    activityId: activityId,
    startTime,
    endTime,
  };

  return (
    <div>
      <Tab activeKey={activeTab} onChange={setActiveTab}>
        <Tab.Item title="中奖记录" key="receive">
          <ReceiveData x-if={activeTab === 'receive'} {...props} />
        </Tab.Item>
        <Tab.Item title="实物中奖信息" key="award">
          <AwardData x-if={activeTab === 'award'} {...props} />
        </Tab.Item>
        <Tab.Item title="抽签码获取明细" key="draw">
          <DrawData x-if={activeTab === 'draw'} {...props} />
        </Tab.Item>
        <Tab.Item title="活动数据" key="activity">
          <ActivityData x-if={activeTab === 'activity'} {...props} />
        </Tab.Item>
        <Tab.Item title="奖品数据" key="prize">
          <PrizeData x-if={activeTab === 'prize'} {...props} />
        </Tab.Item>
      </Tab>
    </div>
  );
}