import { useEffect, useState } from 'react';
import { <PERSON>, <PERSON><PERSON>, Card, DatePicker2, Table, Pagination, Message } from '@alifd/next';
import { dataGetByDayDataExport, dataGetByDayData } from '@/api/v10003';
import { downloadExcel } from '@/utils';
import dayjs from 'dayjs';
import { Auth } from '@/components/Auth';
import usePagination from '@/hooks/usePagination';


const pageNum = 1;
const pageSize = 10;

export default function ActivityDataTable({ activityId, startTime, endTime }) {
  const defaultSearchParams = {
    activityId,
    timeRange: [startTime, endTime],
    startTime: startTime,
    endTime: endTime,
    queryType: '1,2,3,4,7,5,6,8,9,10,11,12,13',
  };
  // 搜索相关
  const [searchParams, setSearchParams] = useState({
    ...defaultSearchParams,
  });

  // 表格相关
const [tableData, setTableData] = useState<any>([]);

const [columns, setColumns] = useState<any>([]);
const [loading, setLoading] = useState(false);

// 通用的参数更新函数
const updateSearchParam = (key: string, value: any) => {
  setSearchParams(prev => ({ ...prev, [key]: value }));
};


const pagination = usePagination(pageNum, pageSize, async (current, size, params) => {
  await fetchData({
    pageNum: current,
    pageSize: size,
    ...params,
  });
});

const fetchData = async (params?: any) => {
  setLoading(true);
  try {
    const [start, end] = searchParams.timeRange || [];

    // 构建查询参数
    const queryParams = {
      ...searchParams,
      startTime: start ? dayjs(start).format('YYYY-MM-DD 00:00:00') : '',
      endTime: end ? dayjs(end).format('YYYY-MM-DD 23:59:59') : '',
      pageNum: pagination.current,
      pageSize: pagination.pageSize,
    };

    const { records, total, titles }: any = await dataGetByDayData({
      ...queryParams,
      ...params,
    });

    // 处理新的数据格式
    const titlesArray = titles || [];
    const recordsArray = records || [];

    // 将titles转换为columns格式
    const columnsData = titlesArray.map((title, index) => ({
      name: title,
      dataIndex: `col_${index}`, // 使用索引作为dataIndex
    }));
    setColumns(columnsData);

    // 将二维数组records转换为对象数组
    const tableDataFormatted = recordsArray.map((row, rowIndex) => {
      const rowObj: any = { key: rowIndex };
      row.forEach((value, colIndex) => {
        rowObj[`col_${colIndex}`] = value;
      });
      return rowObj;
    });
    setTableData(tableDataFormatted);
    pagination.setTotal(total || 0);
  } catch (e) {
    Message.error(e.message);
  } finally {
    setLoading(false);
  }
};

const handleExport = async () => {
  setLoading(true);
  try {
    const [start, end] = searchParams.timeRange || [];
    const data: any = await dataGetByDayDataExport({
      ...searchParams,
      startTime: start ? dayjs(start).format('YYYY-MM-DD 00:00:00') : '',
      endTime: end ? dayjs(end).format('YYYY-MM-DD 23:59:59') : '',
      pageNum: pagination.current,
      pageSize: pagination.pageSize,
    });
    downloadExcel(data, `活动数据明细${dayjs().format('YYYY-MM-DD HH:mm')}`);
  } catch (e) {
    Message.error(e.message);
  } finally {
    setLoading(false);
  }
};

const handleSearch = () => {
  pagination.changePage(pageNum);
};

const handleReset = () => {
  setSearchParams(defaultSearchParams);
  pagination.reset(defaultSearchParams);
};

useEffect(() => {
  fetchData().then();
}, []);

  return (
    <Card
      title="数据明细"
      subTitle={`统计截止：${dayjs().format('YYYY-MM-DD HH:mm:ss')}`}
      contentHeight={'auto'}
      showTitleBullet={false}
      showHeadDivider={false}
      hasBorder={false}
    >
      <Box spacing={16}>
        {/* 查询区 */}
        <Box
          direction="row"
          spacing={10}
          align="center"
        >
          <DatePicker2.RangePicker
            value={[searchParams.timeRange[0], searchParams.timeRange[1]]}
            onChange={([start, end]) => {
              updateSearchParam('timeRange', [start, end]);
            }}
            outputFormat="YYYY-MM-DD"
            hasClear={false}
            disabledDate={date => {
              const start = dayjs(startTime);
              const end = dayjs(endTime);
              return date.isBefore(start, 'day') || date.isAfter(end, 'day');
            }}
          />
          <Button
            type="primary"
            onClick={handleSearch}
          >
            查询
          </Button>
          <Button
            type="normal"
            onClick={handleReset}
          >
            重置
          </Button>
          <Auth authKey={'activity_list_data_export'}>
            <Button
              type="secondary"
              onClick={handleExport}
            >
              导出
            </Button>
          </Auth>
        </Box>

        {/* 表格 */}
        <Table
          dataSource={tableData}
          loading={loading}
          hasBorder
          primaryKey="key"
        >
          {columns.map(col => (
            <Table.Column
              key={col.dataIndex}
              title={col.name}
              dataIndex={col.dataIndex}
              cell={val => val ?? '0'}
            />
          ))}
        </Table>

        <Box direction="row" justify="flex-end">
          {/* 分页 */}
          <Pagination
            {...pagination.paginationProps}
            shape={'arrow-only'}
            totalRender={total => `共 ${total} 条`}
            pageSizeSelector="dropdown"
            pageSizePosition="end"
          />
        </Box>
      </Box>
    </Card>
  );
}
