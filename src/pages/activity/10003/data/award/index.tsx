import Container from '@/components/Container';
import usePagination from '@/hooks/usePagination';
import { maskSensitiveInfo } from '@/utils';
import { Box, Button, DatePicker2, Input, Message, Pagination, Table } from '@alifd/next';
import { useEffect, useState } from 'react';
import dayjs from 'dayjs';
import { dataGetPhysicalPrizeInfo } from '@/api/v10003';

const pageNum = 1;
const pageSize = 10;

export default function AwardData({ activityId }) {
  const defaultSearchParams = {
    activityId,
    openId: '',
    timeRange: [],
    nick: '',
    startTime: '',
    endTime: '',
  };
  // 搜索相关
  const [searchParams, setSearchParams] = useState({
    ...defaultSearchParams,
    activityId,
  });

  // 表格相关
const [tableData, setTableData] = useState<any>([]);
const [loading, setLoading] = useState(false);

// 通用的参数更新函数
const updateSearchParam = (key: string, value: any) => {
  setSearchParams(prev => ({ ...prev, [key]: value }));
};


const pagination = usePagination(pageNum, pageSize, async (current, size, params) => {
  await fetchData({
    pageNum: current,
    pageSize: size,
    ...params,
  });
});

const fetchData = async (params?: any) => {
  setLoading(true);
  try {
    const [start, end] = searchParams.timeRange || [];

    // 构建查询参数
    const queryParams = {
      ...searchParams,
      startTime: start ? dayjs(start).format('YYYY-MM-DD 00:00:00') : '',
      endTime: end ? dayjs(end).format('YYYY-MM-DD 23:59:59') : '',
      pageNum: pagination.current,
      pageSize: pagination.pageSize,
    };

    const { records, total }: any = await dataGetPhysicalPrizeInfo({
      ...queryParams,
      ...params,
    });

    setTableData(records || []);
    pagination.setTotal(total || 0);
  } catch (e) {
    Message.error(e.message);
  } finally {
    setLoading(false);
  }
};

const handleSearch = () => {
  pagination.changePage(pageNum);
};

const handleReset = () => {
  setSearchParams(defaultSearchParams);
  pagination.reset(defaultSearchParams);
};

useEffect(() => {
  fetchData().then();
}, []);

  return (
    <Container style={{ marginTop: 16 }}>
      <Box direction="row" spacing={16} margin={[0, 0, 16, 0]} wrap>
        <Input label={'用户昵称'} value={searchParams.nick} onChange={(value) => updateSearchParam('nick', value)} />
        <Input label={'openId'} value={searchParams.openId} onChange={(value) => updateSearchParam('openId', value)} />
        <DatePicker2.RangePicker
          type={'range'}
          label={'获奖时间'}
          value={searchParams.timeRange}
          onChange={(value) => updateSearchParam('timeRange', value)}
        />
        <Button type="primary" onClick={handleSearch}>查询</Button>
        <Button onClick={handleReset}>重置</Button>
      </Box>
      <Table
        dataSource={tableData}
        loading={loading}
      >
        <Table.Column title="奖品名称" dataIndex="prizeName" />
        <Table.Column
          title="用户昵称"
          dataIndex="nickName"
          cell={(value) => {
            return <div >{value === '匿名' ? value : maskSensitiveInfo(value, 1, 1)}</div>;
        }}
        />
        <Table.Column
          title="openId"
          dataIndex="openId"
          cell={(value) => {
          return <div >{maskSensitiveInfo(value, 1, 1, 6)}</div>;
        }}
        />
        <Table.Column title="获奖时间" dataIndex="createTime" cell={(value) => (value ? dayjs(value).format('YYYY-MM-DD HH:mm:ss') : '-')} />
        <Table.Column
          title="收件人"
          dataIndex="realName"
        />
        <Table.Column
          title="收件人手机号"
          dataIndex="mobile"
          cell={(value) => {
            return maskSensitiveInfo(value, 3, 4) || '-';
          }}
        />
        <Table.Column
          title="省"
          dataIndex="province"
        />
        <Table.Column
          title="市"
          dataIndex="city"
        />
        <Table.Column
          title="区"
          dataIndex="county"
        />
        <Table.Column
          title="详细地址"
          dataIndex="address"
        />
      </Table>
      <Box direction="row" justify="flex-end" margin={[16, 0, 0, 0]}>
        <Pagination
          {...pagination.paginationProps}
          shape={'arrow-only'}
          totalRender={(total) => `共 ${total} 条`}
          pageSizeSelector="dropdown"
          pageSizePosition="end"
        />
      </Box>
    </Container>
  );
}
