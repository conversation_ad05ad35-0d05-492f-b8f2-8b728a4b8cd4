import styles from './index.module.scss';
import Intro from '@/components/Intro';
import Index from '@/components/Container';
import ActivitySkinSelector from './components/ActivitySkinSelector/ActivitySkinSelector';


const instructions = `抽签赢好礼活动，用户通过完成任务可获得的抽签码，到达开奖公示期后抽取对应活动将用户。增强互动，增加粘度，提升转化率。
1、活动效果：会员拉新、新会员促活、提升会员复购转化
2、活动门槛支持设置会员和粉丝，满足活动门槛的买家才可参与活动；奖品支持优惠券、实物奖品、积分
`;

export default function CustomActivity() {
  return (
    <div className={styles.container}>
      <Intro
        activityName="抽签赢好礼"
        docLink="https://gyj4qdmjsv.feishu.cn/wiki/UDKWw35f5im9frkRDNtcg4cgn8b?from=from_copylink"
        instructions={instructions}
      />
      <Index
        title="选择模板"
        style={{ padding: 20, marginTop: 20 }}
      >
        <ActivitySkinSelector activityType={10003} />
      </Index>
    </div>
  );
}
