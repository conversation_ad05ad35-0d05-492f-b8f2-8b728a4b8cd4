.skin-selector {
  display: flex;
  flex-wrap: wrap;

  .template-item {
    display: flex;
    width: fit-content;
    margin: 0 20px 20px 0;
    padding: 20px;
    background-color: #f7f8fa;
    border-radius: 12px;

    &:hover {
      box-shadow: 0 3px 6px rgba(0, 0, 0, 16%);
    }

    .selected-image {
      width: 100%;
      min-height: 100%;
    }

    .template-content {
      padding-right: 20px;
    }

    .template-content-title {
      display: flex;
      padding: 0 0 5px 3px;
      color: #333;
      font-size: 14px;
    }

    .template-tags {
      display: flex;
      padding: 10px 0;

      span {
        margin-right: 15px;
      }
    }

    .select-button {
      margin-top: 16px;
      text-align: right;
    }
  }

  .skin-list {
    position: relative;
    width: 390px;

    .skin-item {
      padding: 4px;

      .skin-item-img {
        width: 100%;
        padding-bottom: 200%;
        background-repeat: no-repeat;
        background-position: center top;
        background-size: 100% auto;
        border: 2px solid transparent;
        border-radius: 4px;

        &.template-0004 {
          padding-bottom: 200%;
        }

        &.template-0002 {
          padding-bottom: 182%;
        }
      }

      &.selected {
        .skin-item-img {
          border: 2px solid var(--primary-color);
        }
      }
    }

    .arrow-button {
      position: absolute;
      top: 50%;
      height: fit-content;
      transform: translateY(-50%);

      &.left {
        left: -30px;
      }

      &.right {
        right: -30px;
      }
    }
  }
}
