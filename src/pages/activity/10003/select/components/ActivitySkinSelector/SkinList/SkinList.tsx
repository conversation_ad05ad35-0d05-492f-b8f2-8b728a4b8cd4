import { useState } from 'react';
import { Slider } from '@alifd/next';

import '../ActivitySkinSelector.scss';

export default function SkinList({ template }) {
  const [sliderIndex, setSliderIndex] = useState(0);

  return (
    <div className="skin-list">
      <Slider
        activeIndex={sliderIndex}
        onChange={setSliderIndex}
        className="skin-slider"
        slidesToShow={3}
        arrows={false}
        dots={false}
        infinite={false}
      >
        <div
          className={'skin-item selected'}
        >
          <div
            className={'skin-item-img'}
            style={{ backgroundImage: `url(${template.cover})` }}
          />
        </div>
      </Slider>

    </div>
  );
}
