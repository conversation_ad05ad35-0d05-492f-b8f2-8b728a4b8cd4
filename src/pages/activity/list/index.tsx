import { useState, useEffect } from 'react';
import { Box, Button, Input, Select, DatePicker2, Table, Pagination, Tab, Message, Dialog, Balloon } from '@alifd/next';
import dayjs from 'dayjs';
import { history } from 'ice';
import store from '@/store';

import TimeDiff from '@/components/TimeDiff/TimeDiff';
import showActivityQrcode from '@/pages/activity/custom/create/components/showActivityQrcode/showActivityQrcode';
import { ActivityTypeMapEnum, StatusMapEnum, getStatusLabel, sortByField } from '@/utils';
import {
  activityGetActivityList,
  activityEndAct,
  activityDelAct,
  activityUpdateTjImg,
  activityUpdateActivityOrderBy,
} from '@/api/b';
import Container from '@/components/Container';
import { addDialogRef } from '@/utils/dialogMapper';
import HelpTooltip from '@/components/HelpTooltip/HelpTooltip';
import NumberInput from '@/components/NumberInput/NumberInput';
import constant from '@/utils/constant';
import { activityGetAllActivityTypes } from '@/api/common';

export default function ActivityListIndexPage() {
  const tabs = [
    { tab: '当前活动', key: 'current' },
    { tab: '历史活动', key: 'history' },
  ];

  const [activityTypeList, setActivityTypeList] = useState<any[]>([]);
  const [activityListState, activityListDispatchers] = store.useModel('activityList');
  const {
    currentTab: storeCurrentTab,
    activityName: storeActivityName,
    activityType: storeActivityType,
    activityStatus: storeActivityStatus,
    createRange: storeCreateRange,
    pageNo: storePageNo,
    pageSize: storePageSize,
    hasStoredState,
  } = activityListState;

  // 使用 store 中的值初始化本地状态，如果 store 中有保存的状态
  const [currentTab, setCurrentTab] = useState(hasStoredState ? storeCurrentTab : 'current');
  // 筛选
  const [activityName, setActivityName] = useState<any>(hasStoredState ? storeActivityName : '');
  const [activityType, setActivityType] = useState<any>(hasStoredState ? storeActivityType : 0);
  const [activityStatus, setActivityStatus] = useState<any>(hasStoredState ? storeActivityStatus : 0);
  const [createRange, setCreateRange] = useState<any[]>(hasStoredState ? storeCreateRange : []);
  // 表格+分页
  const [orderType, setOrderType] = useState(''); // 排序类型
  const [tableData, setTableData] = useState<any[]>([]);
  const [total, setTotal] = useState(0);
  const [pageNo, setPageNo] = useState(hasStoredState ? storePageNo : 1);
  const [pageSize, setPageSize] = useState(hasStoredState ? storePageSize : 10);
  const [loading, setLoading] = useState(false);

  const { shopId } = JSON.parse(localStorage.getItem(constant.LZ_CURRENT_SHOP) || '{}');
  // 保存当前状态到 store
  const saveStateToStore = () => {
    activityListDispatchers.updateState({
      currentTab,
      activityName,
      activityType,
      activityStatus,
      createRange,
      pageNo,
      pageSize,
      hasStoredState: true,
    });
  };

  const fetchList = async (customParams: any = {}) => {
    setLoading(true);
    try {
      // 如果传入了自定义参数，则使用自定义参数；否则使用组件状态中的值
      const params: any = {
        pageNo: String(pageNo),
        pageSize: String(pageSize),
        activityName,
        activityType,
        activityStatus: currentTab === 'history' ? StatusMapEnum.END.value : activityStatus,
        createDateStart: createRange[0] ? dayjs(createRange[0]).format('YYYY-MM-DD') : '',
        createDateEnd: createRange[1] ? dayjs(createRange[1]).format('YYYY-MM-DD') : '',
        ...customParams,
      };
      const res: any = await activityGetActivityList(params);
      // setTableData(res.list || []);
      const records = res.list || [];
      // 如果有排序类型，则进行排序
      if (orderType) {
        setTableData(sortByField(records, 'createTime', orderType));
      } else {
        setTableData(records);
      }
      setTotal(res.total || 0);
    } catch (err) {
      Message.error(err.message);
    } finally {
      setLoading(false);
    }
  };


  const handleReset = (tabKey = currentTab) => {
    // 更新状态时考虑当前tab
    setActivityName('');
    setActivityType(0);
    // 历史活动tab时，强制设置为已结束状态
    setActivityStatus(tabKey === 'history' ? StatusMapEnum.END.value : 0);
    setCreateRange([]);
    setPageSize(10);
    setPageNo(1);
    fetchList({
      pageNo: '1',
      pageSize: '10',
      activityName: '',
      activityType: 0,
      activityStatus: tabKey === 'history' ? StatusMapEnum.END.value : 0,
      createDateStart: '',
      createDateEnd: '',
    });

    // 同时重置 store 中的状态
    activityListDispatchers.resetState();
  };
  function tableSort(dataIndex, order) {
    setOrderType(order);
    if (order == 'desc' || order == 'default') {
      setTableData((tableData) => sortByField(tableData, 'createTime', 'desc'));
    } else if (order == 'asc') {
      setTableData((tableData) => sortByField(tableData, 'createTime', 'asc'));
    }
  }

  /*
    活动二维码
  */
  const onClickQRCode = (record) => {
    console.log('二维码', record);
    showActivityQrcode({ activityInfo: record });
  };

  /**
   * 跳转到编辑页
   */
  const onClickEdit = (record) => {
    console.log('修改', record);
    // 找到当前活动类型对应的路由配置
    const typeConfig = Object.values(ActivityTypeMapEnum).find((item) => item.value === record.activityType);

    // 在跳转前保存当前状态
    saveStateToStore();

    const { activityId, activityStatus, version, activityType } = record;
    if (version === 'v1') {
      if (!typeConfig) {
        Message.error('未找到对应的活动类型路由');
        return;
      }
      history?.push('/activity/custom/create/', { activityId, activityStatus, operationType: 'edit' });
    } else {
      history?.push(`/activity/${activityType}/create/`, { operationType: 'edit', activityId });
    }
  };

  /**
   * 跳转到查看页
   */
  const onClickView = (record) => {
    console.log('查看', record);
    // 找到当前活动类型对应的路由配置
    const typeConfig = Object.values(ActivityTypeMapEnum).find((item) => item.value === record.activityType);

    // 在跳转前保存当前状态
    saveStateToStore();

    const { activityId, activityStatus, version, activityType } = record;
    if (version === 'v1') {
      if (!typeConfig) {
        Message.error('未找到对应的活动类型路由');
        return;
      }
      history?.push('/activity/custom/create/', { activityId, activityStatus, operationType: 'view' });
    } else {
      history?.push(`/activity/${activityType}/create/`, { operationType: 'view', activityId });
    }
  };

  /*
    复制活动
  */
  const onClickCopy = (record) => {
    console.log('复制', record);

    Dialog.confirm({
      title: '复制活动',
      content: '复制活动时，会保留当前活动的所有设置，仅不复制奖品设置和活动规则部分',
      onOk: async () => {
        const typeConfig = Object.values(ActivityTypeMapEnum).find((item) => item.value === record.activityType);
        // 在跳转前保存当前状态
        saveStateToStore();
        const { activityId, activityStatus, version, activityType } = record;
        if (version === 'v1') {
          if (!typeConfig) {
            Message.error('未找到对应的活动类型路由');
            return;
          }
          history?.push('/activity/custom/create/', { activityId, activityStatus, operationType: 'copy' });
        } else {
          history?.push(`/activity/${activityType}/create/`, { operationType: 'copy', activityId });
        }
      },
    });
  };

  /*
    活动推广
  */
  const onClickPromo = (record) => {
    console.log('活动推广', record);

    // 在跳转前保存当前状态
    saveStateToStore();

    history?.push('/activity/promotion/');
  };

  /*
    推广数据
  */
  const onClickPromoData = async (record) => {
    console.log('推广数据', record);

    // 在跳转前保存当前状态
    saveStateToStore();

    history?.push('/activity/promotion/data/', {
      record,
    });
  };

  /*
    活动数据
  */
  const onClickActivityData = (record) => {
    console.log('活动数据', record);

    const { activityType } = record;
    // 在跳转前保存当前状态
    saveStateToStore();

    if (activityType === 1) {
      history?.push('/activity/data/', {
        record,
      });
    } else {
      history?.push(`/activity/${activityType}/data/`, {
        record,
      });
    }

  };

  // 活动记录
  const onClickRecord = (record) => {
    console.log('活动数据', record);

    // 在跳转前保存当前状态
    saveStateToStore();
    history?.push(`/activity/${record.activityType}/record/`, {
      record,
    });
    // history?.push('/activity/data/', {
    //   record,
    // });
  };

  /**
   * 跳转到查看页
   */
  const onClickEnd = (record) => {
    console.log('结束', record);
    const dialogRef = Dialog.confirm({
      title: '是否手动结束活动',
      content: '结束后用户将无法参与活动，确定结束吗？',
      onOk: async () => {
        const { activityId } = record;
        try {
          await activityEndAct({ activityId });
          Message.success({
            content: '结束活动成功！',
            duration: 1000,
            afterClose: () => {
              fetchList();
            },
          });
        } catch (e) {
          Message.error(e.errorMessage);
        }
      },
    });
    addDialogRef(dialogRef);
  };

  /*
    更新活动商品图
  */
  const onClickUpdateImage = (record) => {
    console.log('更新活动商品图', record);
    const dialogRef = Dialog.confirm({
      title: '提示',
      content: '是否要更新活动内商品图？',
      onOk: async () => {
        try {
          await activityUpdateTjImg({ activityId: record.activityId });
          Message.success({
            content: '更新活动商品成功！',
            duration: 1000,
          });
        } catch (e) {
          Message.error(e.errorMessage || e.message);
        }
      },
    });
    addDialogRef(dialogRef);
  };

  /**
   * 删除活动
   */
  const onClickDelete = (record) => {
    console.log('删除', record);
    Dialog.confirm({
      title: '是否删除活动',
      content: '删除后活动将无法找回，确定删除吗？',
      onOk: async () => {
        try {
          await activityDelAct({ activityId: record.activityId });
          Message.success({
            content: '删除活动成功！',
            duration: 1000,
            afterClose: () => {
              fetchList();
            },
          });
        } catch (e) {
          Message.error(e.errorMessage);
        }
      },
    });
  };


  const vfShopIds = [7778309, 20421521, 45693550];
  // 根据状态渲染对应按钮
  /*
    未开始：二维码/修改/复制/活动推广/推广数据/结束/更新活动商品图
    进行中：二维码/修改/复制/活动推广/推广数据/活动数据/结束/更新活动商品图
    已结束：二维码/查看/复制/删除/推广数据/活动数据/
  */
  // 按钮映射：key -> 文案 & 回调
  const BUTTON_CONFIG = {
    qr: { label: '二维码', handler: onClickQRCode },
    edit: { label: '修改', handler: onClickEdit },
    view: { label: '查看', handler: onClickView },
    copy: { label: '复制', handler: onClickCopy },
    promo: { label: '活动推广', handler: onClickPromo, shopIds: vfShopIds },
    promoData: { label: '推广数据', handler: onClickPromoData, shopIds: vfShopIds },
    actData: { label: '活动数据', handler: onClickActivityData },
    end: { label: '结束', handler: onClickEnd },
    updImg: { label: '更新活动商品图', handler: onClickUpdateImage },
    delete: { label: '删除', handler: onClickDelete },
  };

  // 各状态按钮 key 数组
  const OPERATIONS_BY_STATUS = {
    [StatusMapEnum.NOT_START.value]: ['qr', 'edit', 'copy', 'promo', 'promoData', 'end', 'updImg'],
    [StatusMapEnum.IN_PROGRESS.value]: ['qr', 'edit', 'copy', 'promo', 'promoData', 'actData', 'end', 'updImg'],
    [StatusMapEnum.END.value]: ['qr', 'copy', 'promoData', 'actData', 'view', 'delete'],
  };

  function renderOperations(record) {
    const keys = OPERATIONS_BY_STATUS[record.activityStatus] || [];
    return (
      <Box direction="row" spacing={8} wrap>
        {keys.map((key) => {
          const buttonConfig = BUTTON_CONFIG[key];
          if (buttonConfig?.shopIds && !buttonConfig.shopIds.includes(shopId)) {
            return null;
          }
          return (
            <Button key={key} type="primary" text onClick={() => buttonConfig.handler(record)}>
              {buttonConfig.label}
            </Button>
          );
        })}
      </Box>
    );
  }

  const handleUpdateOrderBy = async (activityId, orderBy) => {
    try {
      setLoading(true);
      await activityUpdateActivityOrderBy({ activityId, orderBy });
      await fetchList();
    } catch (err) {
      console.error('updateActivityOrder error:', err);
      Message.error(err.message);
    } finally {
      setLoading(false);
    }
  };

  const getActivityTypeList = async () => {
    const res = await activityGetAllActivityTypes();
    setActivityTypeList(res as any);
  };

  const [editingOrderBy, setEditingOrderBy] = useState(0);
  const renderOrderBy = (record) => {
    return (
      <Box direction="row" align="center" spacing={10}>
        <span>{record.orderBy}</span>
        <Balloon
          v2
          trigger={
            <Button text type="primary" iconSize="medium">
              <i style={{ lineHeight: 1.3 }} className="iconfont icon-bianji" />
            </Button>
          }
          onVisibleChange={(visible) => {
            if (visible) {
              setEditingOrderBy(record.orderBy);
            }
          }}
          triggerType="click"
          title="修改优先级"
        >
          <Box direction="row" align="center" spacing={10}>
            <NumberInput
              min={0}
              max={999}
              type="inline"
              value={editingOrderBy}
              style={{ width: 120 }}
              onChange={(value: number) => setEditingOrderBy(value)}
            />
            <Button type="primary" size="small" onClick={() => handleUpdateOrderBy(record.activityId, editingOrderBy)}>
              保存
            </Button>
          </Box>
        </Balloon>
      </Box>
    );
  };

  const handlePageChange = (p) => {
    setPageNo(p);
    fetchList({
      pageNo: String(p),
    });
  };

  const handleSearch = () => {
    setPageNo(1);
    fetchList({
      pageNo: String(1),
    });
  };

  const handlePageSizeChange = (size: number) => {
    setPageSize(size);
    fetchList({
      pageNo: String(1),
      pageSize: String(size),
    });
  };

  const getActivityTypeLabel = (activityType) => {
    for (const key in activityTypeList) {
      if (activityTypeList[key].value === activityType) {
        return activityTypeList[key].label;
      }
    }
    return '-';
  };

  useEffect(() => {
    getActivityTypeList();
    fetchList();
  }, []);

  return (
    <Box spacing={16}>
      <Tab
        activeKey={currentTab}
        onChange={(key) => {
          // 切换Tab时重置store中的缓存
          setCurrentTab(key);
          handleReset(key); // 传入新的tab key
        }}
      >
        {tabs.map((item) => (
          <Tab.Item key={item.key} title={item.tab} />
        ))}
      </Tab>
      <Container>
        <Box spacing={16}>
          {/* 筛选区 */}
          <Box direction="row" spacing={16}>
            <Input
              label="活动名称"
              placeholder="活动名称"
              trim
              composition
              value={activityName}
              onChange={setActivityName}
              style={{ width: 200 }}
            />
            <Select
              label="活动类型"
              placeholder="活动类型"
              value={activityType}
              onChange={setActivityType}
              style={{ width: 200 }}
            >
              <Select.Option value={0}>全部类型</Select.Option>
              {activityTypeList?.map((item) => (
                <Select.Option key={item.value} value={item.value}>
                  {item.label}
                </Select.Option>
              ))}
            </Select>
            {currentTab === 'current' && (
              <Select
                label="活动状态"
                placeholder="活动状态"
                value={activityStatus}
                onChange={setActivityStatus}
                style={{ width: 180 }}
              >
                <Select.Option value={0}>全部状态</Select.Option>
                {[StatusMapEnum.NOT_START.value, StatusMapEnum.IN_PROGRESS.value].map((st) => (
                  <Select.Option key={st} value={st}>
                    {getStatusLabel(st)}
                  </Select.Option>
                ))}
              </Select>
            )}
            <DatePicker2.RangePicker
              label="创建时间"
              placeholder={['开始日期', '结束日期']}
              outputFormat="YYYY-MM-DD"
              value={createRange}
              onChange={setCreateRange}
            />
            <Button type="primary" onClick={handleSearch}>
              查询
            </Button>
            <Button onClick={() => handleReset()}>重置</Button>
          </Box>

          {/* 列表 */}
          <Table dataSource={tableData} onSort={tableSort} loading={loading} hasBorder>
            <Table.Column title="活动名称" dataIndex="activityName" />
            <Table.Column title="活动类型" dataIndex="activityType" cell={(value) => getActivityTypeLabel(value)} />
            <Table.Column title="创建时间" sortable sortDirections={['desc', 'asc']} dataIndex="createTime" />
            <Table.Column
              title="有效期"
              cell={(_, __, record) => {
                return <TimeDiff startTime={record.startTime} endTime={record.endTime} />;
              }}
            />
            {currentTab === 'current' && (
              <Table.Column
                title={
                  <Box direction="row" align="center" spacing={5}>
                    <span>活动优先级</span>
                    <HelpTooltip content={'当用户一笔订单满足多个活动时，仅发放优先级较高活动奖励（随单发奖品）'} />
                  </Box>
                }
                dataIndex="orderBy"
                cell={(_, __, record) => renderOrderBy(record)}
              />
            )}
            <Table.Column
              title="活动状态"
              cell={(_, __, record) => {
                const status = record.activityStatus;
                // 状态 -> 类名的映射表
                const statusClassMap = {
                  [StatusMapEnum.END.value]: 'status-before', // 已结束
                  [StatusMapEnum.NOT_START.value]: 'status-blue', // 未开始
                  [StatusMapEnum.IN_PROGRESS.value]: 'status-normal', // 进行中
                };
                const cls = statusClassMap[status] || '';
                return <span className={cls}>{getStatusLabel(status)}</span>;
              }}
            />
            <Table.Column title="操作" cell={(_, __, record) => renderOperations(record)} />
          </Table>

          {/* 分页 */}
          <Box direction="row" justify="flex-end">
            <Pagination
              current={pageNo}
              pageSize={pageSize}
              total={total}
              shape={'arrow-only'}
              onChange={(p) => handlePageChange(p)}
              onPageSizeChange={(size) => handlePageSizeChange(size)}
              pageSizeSelector="dropdown"
              pageSizePosition="end"
              totalRender={(t) => `共${t}条`}
            />
          </Box>
        </Box>
      </Container>
    </Box>
  );
}
