import dayjs from 'dayjs';
import { Dialog, Message } from '@alifd/next';
import constant from '@/utils/constant';
import React from 'react';

// 复制
export const copyText = (text) => {
  const tag = document.createElement('input');
  tag.setAttribute('id', 'cp_hgz_input_new');
  tag.value = text;
  document.getElementsByTagName('body')[0].appendChild(tag);
  // eslint-disable-next-line @typescript-eslint/ban-ts-comment
  // @ts-ignore
  document.getElementById('cp_hgz_input_new').select();
  document.execCommand('copy');
  // eslint-disable-next-line @typescript-eslint/ban-ts-comment
  // @ts-ignore
  document.getElementById('cp_hgz_input_new').remove();
  Message.success('复制成功');
};

// 活动类型枚举
export const ActivityTypeMapEnum = {
    CUSTOMIZE_ACTIVITY: { label: '自定义活动', value: 1 },
    TURN_TABLE: { label: '抽奖活动', value: 10002 },
};

// 下载 Excel 文件
export const downloadExcel = (data: { file: Blob; suffix: string }, fileName?: string) => {
  console.log(data);
  const { file, suffix } = data;
  const link: HTMLAnchorElement = document.createElement('a');
  link.style.display = 'none';
  link.href = window.URL.createObjectURL(file);
  link.setAttribute(
    'download',
    fileName && fileName !== '' ? `${fileName}${suffix}` : `导出数据${dayjs().format('YYYY-MM-DD')}${suffix}`,
  );
  document.body.appendChild(link);
  link.click();
};

// 奖品类型枚举
export const PrizeTypeEnum = Object.freeze({
  PRACTICALITY: { label: '实物（手工发货）', value: 7 },
  PRACTICALITY_BY_ORDER: { label: '实物（随单发货）', value: 6 },
  SPECIAL: { label: '实物（专属下单）', value: 8 },
  COUPON: { label: '优惠券', value: 3 },
  ALIPAY_RED_PACKET: { label: '平台支付宝红包', value: 1 },
  RED_PACKET: { label: '红包（电商专享）', value: 1030 },
  MEMBER_POINT: { label: '会员积分', value: 9 },
  THANKS: { label: '谢谢参与', value: 0 },
  // MEMBER_COUPON: { label: '会员专享券', value: 4 },
});

/**
 * 通用数据脱敏方法
 * @param value 需要脱敏的字符串
 * @param prefixLength 保留前几位 默认1位
 * @param suffixLength 保留后几位 默认1位
 * @returns 脱敏后的字符串
 */
export const maskSensitiveInfo = (value: string, prefixLength = 1, suffixLength = 1, maskLength = 4): string => {
  if (!value) return '';

  // 将字符串转换为数组以正确处理表情符号等Unicode字符
  const chars = [...value];

  // 如果字符串长度小于等于(前缀+后缀)位，不做脱敏处理
  // if (chars.length <= prefixLength + suffixLength) {
  //   return value;
  // }

  const prefix = chars.slice(0, prefixLength).join('');
  const suffix = chars.slice(chars.length - suffixLength).join('');
  const mask = '*'.repeat(maskLength);

  return `${prefix}${mask}${suffix}`;
};


// 活动状态枚举
export const StatusMapEnum = {
  NOT_START: { label: '未开始', value: 1 },
  IN_PROGRESS: { label: '进行中', value: 2 },
  END: { label: '已结束', value: 3 },
};

// 活动时间差计算
export const timeDiff = (startTime, endTime) => {
  if (!startTime || !endTime) {
    return '';
  }
  const start = dayjs(startTime);
  const end = dayjs(endTime);
  const days = end.diff(start, 'day');
  const hours = end.diff(start, 'hour') % 24;

  return hours === 0 ? `${days} 天` : `${days} 天 ${hours} 小时`;
};

// 显示错误信息对话框
export const showErrorMessageDialog = errors => {
  // 统一提取错误数组
  const errorList = Array.isArray(errors) ? errors : Object.values(errors).flatMap((item: any) => item.errors || []);

  const errorMessage = (
    <div className="error-message">
      {errorList.map((item, index) => (
        <p key={index}>{item}</p>
      ))}
    </div>
  );

  Dialog.error({
    v2: true,
    width: 500,
    content: errorMessage,
  });
};

// 获取活动类型标签
// eg: getActivityTypeLabel(ActivityTypeMapEnum.CUSTOMIZE_ACTIVITY.value) => '自定义活动'
export const getActivityTypeLabel = activityType => {
  for (const key in ActivityTypeMapEnum) {
    if (ActivityTypeMapEnum[key].value === activityType) {
      return ActivityTypeMapEnum[key].label;
    }
  }
  return '--';
};


// 获取奖品类型标签
// eg: getPrizeTypeLabel(PrizeTypeEnum.MEMBER_POINT.value) => '会员积分'
export const getPrizeTypeLabel = lotteryType => {
  // 遍历 PrizeTypeEnum 对象，找到 value 相等的枚举项
  for (const key in PrizeTypeEnum) {
    if (PrizeTypeEnum[key].value === lotteryType) {
      return PrizeTypeEnum[key].label;
    }
  }
  return '-';
};

// 非空判断
export const isVoid = value => value === undefined || value === null || value === '';

// 获取活动状态标签
// eg: getStatusLabel(StatusMapEnum.NOT_START.value) => '未开始'
export const getStatusLabel = value => {
  const entry = Object.values(StatusMapEnum).find(e => e.value == value);
  return entry ? entry.label : '-';
};

// 对数组按指定字段进行排序
export function sortByField(array, sortField = 'createTime', sortOrder = 'desc') {
  const newArray = [...array]; // 创建一个新的数组副本
  return newArray.sort((a, b) => {
    const dateA = dayjs(a[sortField]).valueOf();
    const dateB = dayjs(b[sortField]).valueOf();
    if (sortOrder === 'asc') {
      return dateA - dateB; // 升序
    } else {
      return dateB - dateA; // 降序，默认为此
    }
  });
}

export const getExpireDay = (): number => {
  const appInfo = JSON.parse(localStorage.getItem(constant.LZ_CURRENT_SHOP) || '{}');
  const { orderExpireTime } = appInfo;
  console.log(dayjs(orderExpireTime).diff(dayjs(), 'day'), 'orderExpireTime');
  return dayjs(orderExpireTime).diff(dayjs(), 'day');
};

export const formatDateRange = (dateRange: any) => {
  const format = 'YYYY-MM-DD HH:mm:ss';
  return `${dayjs(dateRange[0]).format(format)} 至 ${dayjs(dateRange[1]).format(format)}`;
};
// 提取JSX元素的文本内容
export const extractTextFromJSX = (jsxElement: React.ReactNode): string => {
  // 如果是字符串或数字，直接返回
  if (typeof jsxElement === 'string' || typeof jsxElement === 'number') {
    return String(jsxElement);
  }

  // 如果是有效的React元素
  if (React.isValidElement(jsxElement)) {
    // 获取子元素内容
    const children = jsxElement.props?.children;

    if (children) {
      // 如果子元素是数组，递归处理每个子元素并拼接
      if (Array.isArray(children)) {
        return children.map(child => extractTextFromJSX(child)).join('');
      }
      // 递归处理子元素
      return extractTextFromJSX(children);
    }
    return '';
  }

  // 处理其他类型
  return String(jsxElement || '');
};
// 数字千分位格式化
export const toThousands = num => (num || 0).toString().replace(/(\d)(?=(?:\d{3})+$)/g, '$1,');
