import { Box, Button, Field, Form, Input, Message, Radio } from '@alifd/next';
import HelpTooltip from '@/components/HelpTooltip/HelpTooltip';
import NumberInput from '@/components/NumberInput/NumberInput';
import { useState } from 'react';
import { TaskType } from '@/components/Task/util';


const defaultFormData = {
  taskTitle: '邀请好友参与并入会', // 任务标题
  taskDesc: '', // 任务描述
  taskTimeType: 1, // 任务时间类型 1:活动期间 2:指定时间
  inviteCount: 1, // 邀请人数
  unitCount: 1, // 单次完成任务赠送抽奖次数
  limitTimes: 1, // 活动期间内完成上限
};


export default function InviteFriendJoin({ onSave, layout, initialData, unit, unitName, activityType }) {
  const tipPanel = (
    <div>
      <div style={{ fontWeight: 'bold' }}>任务说明</div>
      <div>1. 用户邀请的好友需【成功报名】才可视为：已参与活动</div>
      <div>2.  如被邀请人是非会员，完成入会后邀请人数+1，满足条件后发放对应{unitName}</div>
      <div>3.  如被邀请人已是会员，则邀请人数不+1</div>
    </div>
  );
  const field = Field.useField();
  const [formData, setFormData] = useState({
    ...defaultFormData,
    ...initialData,
    taskType: TaskType.INVITE_FRIEND_JOIN,
  });

  const handleChange = (field: string, value: any) => {
    setFormData({
      ...formData,
      [field]: value,
    });
  };

  const handleSave = () => {
    field.validate((errors) => {
      console.log(errors);
      if (errors) {
        return;
      }
      onSave(formData);
    });
  };

  return (
    <Form {...layout} field={field}>
      <Message type={'notice'} style={{ marginBottom: 16 }}>
        {tipPanel}
      </Message>
      <Form.Item
        label={'任务时间'}
      >
        <Radio.Group
          value={formData.taskTimeType}
          onChange={value => handleChange('taskTimeType', value)}
        >
          <Radio value={1}>活动期间</Radio>
        </Radio.Group>
      </Form.Item>
      <Form.Item label={'任务条件'}>
        <Box direction={'row'} align={'center'} spacing={8}>
          每成功邀请
          <NumberInput
            min={1}
            value={formData.shareCount}
            onChange={(value: number) => handleChange('shareCount', value)}
          />
          个好友参与活动并入会，即可获得
          <NumberInput
            min={1}
            max={activityType === 10003 ? 999 : 9999}
            value={formData.unitCount}
            onChange={(value: number) => handleChange('unitCount', value)}
          />
          {unit}{unitName}
        </Box>
      </Form.Item>
      <Form.Item label={'活动时间内完成上限'}>
        <Box direction={'row'} align={'center'} spacing={8}>
          限制
          <NumberInput
            min={1}
            value={formData.limitTimes}
            onChange={value => handleChange('limitTimes', value)}
          />
          次
          <HelpTooltip content={'用户在活动时间内可完成该任务的次数'} />
        </Box>
      </Form.Item>

      <Form.Item label={'任务标题(C端展示)'} name={'taskTitle'} required requiredMessage="请输入任务标题">
        <Input maxLength={16} showLimitHint value={formData.taskTitle} onChange={value => handleChange('taskTitle', value)} />
      </Form.Item>
      <Form.Item label={'任务描述(C端展示)'} name={'taskDesc'} required requiredMessage="请输入任务描述">
        <Input.TextArea maxLength={18} showLimitHint rows={1} value={formData.taskDesc} onChange={value => handleChange('taskDesc', value)} />
      </Form.Item>

      <Box direction={'row'} justify={'center'} spacing={8} style={{ marginTop: 16 }}>
        <Button onClick={() => onSave()}>取消</Button>
        <Button type={'primary'} style={{ width: 100 }} onClick={handleSave}>确定</Button>
      </Box>
    </Form>
  );
}
