import { Box, Button, Field, Form, Input, Message, Radio } from '@alifd/next';
import HelpTooltip from '@/components/HelpTooltip/HelpTooltip';
import NumberInput from '@/components/NumberInput/NumberInput';
import ProductPicker from '@/components/ProductPicker';
import { useState } from 'react';
import { TaskType } from '@/components/Task/util';


const defaultFormData = {
  taskTitle: '邀请好友参与并下单', // 任务标题
  taskDesc: '', // 任务描述
  taskTimeType: 1, // 任务时间类型 1:活动期间 2:指定时间
  inviteCount: 1, // 邀请人数
  unitCount: 1, // 单次完成任务赠送抽奖次数
  totalAmount: 1, // 累计订单金额
  limitTimes: 1, // 活动期间内完成上限
  orderStatus: 1, // 订单状态 1:已付款 2:已完成
  orderType: 1, // 订单类型 1:现货订单
  productType: 1, // 商品类型 1:全店商品 2:排除部分商品 3:指定商品
  selectedProducts: [], // 选择的商品
};


export default function InviteFriendOrder({ onSave, layout, initialData, unit, unitName, activityType }) {
  const tipPanel = (
    <div>
      <div style={{ fontWeight: 'bold' }}>任务说明</div>
      <div>1. 用户邀请的好友需【成功报名】才可视为：已参与活动</div>
      <div>2. 参与后下单满足累计金额即视为邀请人数+1，满足条件后发放对应{unitName}</div>
      <div>3.  被邀请人退款导致金额不足，邀请人数-1，获得的{unitName}失效</div>
    </div>
  );
  const field = Field.useField();
  const [formData, setFormData] = useState({
    ...defaultFormData,
    ...initialData,
    taskType: TaskType.INVITE_FRIEND_ORDER,
  });

  const handleChange = (field: string, value: any) => {
    setFormData({
      ...formData,
      [field]: value,
    });
  };

  const handleSave = () => {
    field.validate((errors) => {
      console.log(errors);
      if (errors) {
        return;
      }
      onSave(formData);
    });
  };

  return (
    <Form {...layout} field={field}>
      <Message type={'notice'} style={{ marginBottom: 16 }}>
        {tipPanel}
      </Message>
      <Form.Item
        label={'任务时间'}
      >
        <Radio.Group
          value={formData.taskTimeType}
          onChange={value => handleChange('taskTimeType', value)}
        >
          <Radio value={1}>活动期间</Radio>
        </Radio.Group>
      </Form.Item>

      <Form.Item label={'任务条件'}>
        <HelpTooltip content={'付款时间在【活动期间】或【指定时间内】且 订单状态满足配置 且 订单中包含指定商品则视为满足条件'} />


        <Box direction={'row'} align={'center'} spacing={8} >
          每邀请
          <NumberInput
            min={1}
            value={formData.inviteCount}
            onChange={(value: number) => handleChange('inviteCount', value)}
          />
          个好友参与活动并下单，即可获得
          <NumberInput
            min={1}
            max={activityType === 10003 ? 999 : 9999}
            value={formData.unitCount}
            onChange={(value: number) => handleChange('unitCount', value)}
          />
          {unit}{unitName}
        </Box>
        <Box direction="row" align="center" spacing={8} margin={[16, 0, 0, 0]}>
          被邀好友累计下单金额≥
          <NumberInput
            min={1}
            value={formData.totalAmount}
            onChange={(value: number) => handleChange('totalAmount', value)}
          /> 元
        </Box>

      </Form.Item>

      <Form.Item label={'订单状态'} name="orderStatus">
        <Radio.Group
          value={formData.orderStatus}
          onChange={value => handleChange('orderStatus', value)}
        >
          <Radio value={1}>已付款 <HelpTooltip content={'已付款，已发货，已完成状态均可视为符合条件'} /></Radio>
        </Radio.Group>
      </Form.Item>
      <Form.Item
        label={'订单类型'}
        required
        name="orderTypes"
      >
        <Radio.Group
          value={formData.orderType}
          onChange={value => handleChange('orderType', value)}
        >
          <Radio value={1}>现货订单</Radio>
        </Radio.Group>
      </Form.Item>
      <Form.Item
        label={'订单商品'}
        required
        name="selectedProducts"
        validateState={formData.productType !== 1 && !formData.selectedProducts.length ? 'error' : undefined}
        help={formData.productType !== 1 && !formData.selectedProducts.length ? '请选择订单商品' : undefined}
      >
        <Radio.Group
          value={formData.productType}
          onChange={value => handleChange('productType', value)}
        >
          <Radio value={1}>全店商品</Radio>
          <Radio value={2}>排除部分商品 <HelpTooltip content={'您可以排除例如小样、购物金、补邮费链接或已经参与其他活动的商品，最多排除200件'} /></Radio>
          <Radio value={3}>指定商品</Radio>
        </Radio.Group>
        {/* 自定义组件无法通过field认证 使用Input伪造 */}
        <Input
          x-if={formData.productType !== 1}
          htmlType="hidden"
          name="selectedProducts"
          value={formData.selectedProducts.length > 0 ? '1' : ''}
        />
        <div style={{ marginTop: 16 }} x-if={formData.productType !== 1}>
          <ProductPicker
            min={1}
            max={200}
            selectedItems={formData.selectedProducts}
            onSelectedProducts={(products: any) => {
              handleChange('selectedProducts', products);
              field.setError('selectedProducts', '');
            }}
          />
        </div>
      </Form.Item>
      <Form.Item label={'活动时间内完成上限'}>
        <Box direction={'row'} align={'center'} spacing={8}>
          限制
          <NumberInput
            min={1}
            value={formData.limitTimes}
            onChange={value => handleChange('limitTimes', value)}
          />
          次
          <HelpTooltip content={'用户在活动时间内可完成该任务的次数'} />
        </Box>
      </Form.Item>

      <Form.Item label={'任务标题(C端展示)'} name={'taskTitle'} required requiredMessage="请输入任务标题">
        <Input maxLength={16} showLimitHint value={formData.taskTitle} onChange={value => handleChange('taskTitle', value)} />
      </Form.Item>
      <Form.Item label={'任务描述(C端展示)'} name={'taskDesc'} required requiredMessage="请输入任务描述">
        <Input.TextArea maxLength={18} showLimitHint rows={3} value={formData.taskDesc} onChange={value => handleChange('taskDesc', value)} />
      </Form.Item>

      <Box direction={'row'} justify={'center'} spacing={8} style={{ marginTop: 16 }}>
        <Button onClick={() => onSave()}>取消</Button>
        <Button type={'primary'} style={{ width: 100 }} onClick={handleSave}>确定</Button>
      </Box>
    </Form>
  );
}
