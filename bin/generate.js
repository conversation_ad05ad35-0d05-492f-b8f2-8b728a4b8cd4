const path = require('path');
const fs = require('fs');
const lodash = require('lodash');

const FILE_PREFIX = `/* eslint-disable */
/* tslint:disable */
/*
 * ---------------------------------------------------------------
 * ## THIS FILE WAS GENERATED VIA SWAGGER-TYPESCRIPT-API        ##
 * ##                                                           ##
 * ## AUTHOR: acacode                                           ##
 * ## SOURCE: https://github.com/acacode/swagger-typescript-api ##
 * ---------------------------------------------------------------
 */

`;
const LZ_FILE_PREFIX = '';
// yxf
// const openApiUrl = 'http://*************:8082/v2/api-docs?group=%E5%85%A8%E9%83%A8%E6%8E%A5%E5%8F%A3';
// tjy
// const openApiUrl = 'http://************:8082/v2/api-docs?group=%E5%85%A8%E9%83%A8%E6%8E%A5%E5%8F%A3';
const openApiUrl = 'https://dydz-b-test.lucidata.cn/api/v2/api-docs?group=%E5%85%A8%E9%83%A8%E6%8E%A5%E5%8F%A3';
// const openApiUrl = 'http://127.0.0.1:8082/v2/api-docs?group=%E5%85%A8%E9%83%A8%E6%8E%A5%E5%8F%A3';


const { generateApi } = require('swagger-typescript-api');
const outputDir = path.resolve(process.cwd(), '../src/api');
const options = {
  url: openApiUrl, // openapi接口url
  output: outputDir, // 输出目录
  templates: path.resolve(__dirname, 'templates'), // 模板目录
  modular: true, // 为客户端、数据类型和路由生成单独的文件
  cleanOutput: false, // 清除输出目录
  enumNamesAsValues: false,
  moduleNameFirstTag: false,
  generateUnionEnums: false,
  extractRequestBody: true, // 生成请求体类型
  extractRequestParams: true, // 提取请求参数,将路径参数和查询参数合并到一个对象中
  unwrapResponseData: true, // 从响应中展开数据项 res 或 res.data
  httpClientType: 'axios', // 可选 'fetch'     //http客户端类型
  defaultResponseAsSuccess: false,
  generateClient: true, // 生成http客户端
  generateRouteTypes: false, // 生成路由器类型
  generateResponses: false, // 生成响应类型
  defaultResponseType: 'void',
  typePrefix: '', // 类型前缀
  typeSuffix: '', // 类型后缀
  enumKeyPrefix: '', // 枚举key前缀
  enumKeySuffix: '', // 枚举key后缀
  addReadonly: false, // 设置只读
  /** 允许根据这些额外模板生成额外文件,请参阅下文 */
  extraTemplates: [],
  anotherArrayType: false,
  fixInvalidTypeNamePrefix: 'Type', // 修复无效类型名称前缀
  fixInvalidEnumKeyPrefix: 'Value', // 修复无效枚举键前缀
  hooks: {
    onPrepareConfig: (currentConfiguration) => {
      const { config } = currentConfiguration;
      config.fileNames.httpClient = 'httpClient'; // http客户端文件名
      config.fileNames.dataContracts = 'types'; // 类型文件名
      return { ...currentConfiguration, config };
    },
    onFormatRouteName: (routeInfo, templateRouteName) => {
      if (routeInfo.method === 'get') {
        return `Get${lodash.upperFirst(routeInfo.moduleName)}Request`;
      }
      return templateRouteName;
    },
    // onCreateComponent: (component) => {
    //   console.log('onCreateComponent', component)
    // },
    // onCreateRequestParams: (rawType) => {
    //   console.log('onCreateRequestParams', rawType)
    // },
    // onCreateRoute: (routeData) => {
    //   console.log('onCreateRoute', routeData)
    // },
    // onCreateRouteName: (routeNameInfo, rawRouteInfo) => {
    //   console.log('onCreateRouteName', routeNameInfo, rawRouteInfo)
    // },
    // onFormatTypeName: (typeName, rawTypeName, schemaType) => {
    //   console.log('onFormatTypeName', typeName, rawTypeName, schemaType)
    // },
    onPreParseSchema: (originalSchema, typeName, schemaType) => {
      if (originalSchema.type === 'integer' && originalSchema.format === 'int64') {
        originalSchema.type = 'string';
        originalSchema.format = 'string';
      }
    },
    // onParseSchema: (originalSchema, parsedSchema) => {
    //   console.log('onParseSchema', originalSchema, parsedSchema)
    // },
  },
};

const afterGenerate = ({ files, configuration }) => {
  files.forEach(({ name }) => {
    if (name === 'httpClient.ts') {
      fs.unlinkSync(path.join(outputDir, name));
    } else {
      fs.readFile(path.join(outputDir, name), 'utf8', (err, data) => {
        if (err) throw err;
        const newContent = data?.replace(FILE_PREFIX, LZ_FILE_PREFIX);
        fs.writeFileSync(path.join(outputDir, name), newContent, 'utf8');
      });
      fs.renameSync(
        path.join(outputDir, name),
        path.join(outputDir, name.charAt(0).toLowerCase() + name.substring(1)),
      );
    }
  });
};

generateApi(options).then(afterGenerate);
